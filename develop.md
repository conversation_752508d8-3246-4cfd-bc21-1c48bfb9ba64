# 新媒体投稿系统开发文档

## 项目概述

基于现有的 `art-design-pro-edge` 项目进行二次开发，构建一套完整的新媒体投稿系统。该系统支持用户投稿、管理员审核、发布日历管理等功能，分为管理员端和用户端两个部分。

## 技术架构

### 前端技术栈
- **框架**: Vue 3 + TypeScript + Vite
- **UI库**: Element Plus
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **代码规范**: ESLint + Prettier + Stylelint + Husky
- **构建工具**: Vite
- **包管理**: pnpm

### 后端技术栈
- **语言**: Go
- **框架**: Gin
- **ORM**: GORM
- **数据库**: PostgreSQL
- **缓存**: Redis
- **认证**: JWT
- **部署**: Docker

## 开发阶段总结

### 第一阶段：基础功能开发 ✅

#### 已完成功能

**1. 数据库设计和迁移**
- ✅ 扩展数据模型，新增投稿系统相关表结构
  - `Submission` - 投稿主表
  - `SubmissionFile` - 投稿文件表
  - `SubmissionLog` - 投稿操作日志表
  - `Category` - 分类表
  - `PublishCalendar` - 发布日历表
- ✅ 完善状态管理，定义完整的投稿状态流转枚举
- ✅ 更新数据库迁移，添加新表的自动迁移和初始数据

**2. 后端API基础框架**
- ✅ 数据访问层：创建完整的DAO层
  - `submission.go` - 投稿数据操作
  - `file.go` - 文件数据操作
  - `category.go` - 分类和日历数据操作
- ✅ 控制器层：实现核心API控制器
  - `submission.go` - 投稿CRUD和查询
  - `status.go` - 投稿状态管理和流转
  - `file.go` - 文件上传、下载、管理
- ✅ 路由配置：添加完整的API路由

**3. 前端页面基础结构**
- ✅ 投稿列表页面：基于原版模板创建管理员端投稿列表
- ✅ API接口层：创建完整的投稿管理API封装
- ✅ 路由配置：更新前端路由，添加管理员端和用户端模块

**4. 用户认证和权限控制**
- ✅ JWT认证集成：复用现有的JWT中间件
- ✅ 角色权限控制：管理员和普通用户的权限区分

### 第二阶段：核心功能开发 ✅

#### 已完成功能

**1. 完善后端分类和日历管理API**
- ✅ 创建分类管理控制器 (`category.go`)
  - 分类CRUD操作
  - 分类排序管理
  - 启用/禁用分类
- ✅ 创建日历管理控制器 (`calendar.go`)
  - 发布日历查询和更新
  - 可用发布日期查询
  - 节假日设置
  - 日历统计信息
- ✅ 更新路由配置，添加分类和日历管理路由

**2. 用户端投稿界面**
- ✅ 投稿创建页面 (`/my-submission/create/index.vue`)
  - 完整的投稿表单（标题、类型、描述、内容等）
  - 富文本编辑器集成
  - 文件上传功能
  - 分类选择
  - 草稿保存和投稿提交
- ✅ 用户端投稿列表页面 (`/my-submission/list/index.vue`)
  - 我的投稿列表查询
  - 投稿详情查看
  - 投稿状态跟踪
  - 操作日志展示
  - 文件下载功能

**3. 发布日历页面**
- ✅ 日历视图页面 (`/submission/calendar/index.vue`)
  - 月度日历展示
  - 每日投稿数量和状态显示
  - 日期详情查看
  - 日历设置（最大投稿数、节假日标记）
  - 统计信息展示
  - 节假日批量设置

**4. 完善API接口实现**
- ✅ 更新前端投稿列表页面，实际调用后端API
- ✅ 完善状态管理逻辑
- ✅ 添加批量操作功能

## 数据库设计

### 核心表结构

#### 投稿表 (submissions)
```sql
- id: 主键
- user_id: 投稿用户ID
- title: 投稿标题
- description: 投稿描述
- type: 投稿类型 (article/video/mixed)
- status: 投稿状态 (draft/submitted/reviewing/approved/rejected/revision/scheduled/published/withdrawn)
- priority: 优先级 (low/normal/high/urgent)
- publish_date: 计划发布日期
- tags: 标签
- keywords: 关键词
- view_count: 查看次数
- admin_notes: 管理员备注
- submitted_at: 提交时间
- reviewed_at: 审核时间
- published_at: 发布时间
```

#### 投稿文件表 (submission_files)
```sql
- id: 主键
- submission_id: 投稿ID
- original_name: 原始文件名
- stored_name: 存储文件名
- file_path: 文件路径
- file_type: 文件类型
- file_size: 文件大小
- mime_type: MIME类型
- file_category: 文件分类 (image/video/document)
- description: 文件描述
- sort_order: 排序
```

#### 投稿操作日志表 (submission_logs)
```sql
- id: 主键
- submission_id: 投稿ID
- user_id: 操作用户ID
- action: 操作类型
- old_status: 原状态
- new_status: 新状态
- comment: 操作备注
```

#### 分类表 (categories)
```sql
- id: 主键
- name: 分类名称
- description: 分类描述
- sort_order: 排序
- is_active: 是否启用
```

#### 发布日历表 (publish_calendars)
```sql
- id: 主键
- publish_date: 发布日期
- max_submissions: 最大投稿数量
- current_submissions: 当前投稿数量
- notes: 备注
- is_holiday: 是否节假日
```

## 投稿状态流转

```mermaid
stateDiagram-v2
    [*] --> 草稿: 用户创建
    草稿 --> 已提交: 用户提交
    草稿 --> [*]: 用户删除
    
    已提交 --> 审核中: 管理员开始审核
    已提交 --> 草稿: 用户撤回
    已提交 --> 已拒绝: 管理员拒绝
    
    审核中 --> 已通过: 管理员通过
    审核中 --> 已拒绝: 管理员拒绝
    审核中 --> 需修改: 管理员要求修改
    
    需修改 --> 草稿: 用户修改
    需修改 --> 已提交: 用户重新提交
    
    已通过 --> 待发布: 管理员安排发布
    已通过 --> 已拒绝: 管理员取消通过
    
    待发布 --> 已发布: 管理员发布
    待发布 --> 已通过: 管理员取消发布计划
    
    已发布 --> 已撤回: 管理员撤回
    
    已拒绝 --> 草稿: 用户重新编辑
    已撤回 --> 待发布: 管理员重新发布
```

## API接口设计

### 投稿管理API
- `GET /api/v1/admin/submission/list` - 获取投稿列表
- `GET /api/v1/admin/submission/detail` - 获取投稿详情
- `POST /api/v1/admin/submission/create` - 创建投稿
- `PUT /api/v1/admin/submission/update` - 更新投稿
- `DELETE /api/v1/admin/submission/delete` - 删除投稿
- `PUT /api/v1/admin/submission/submit` - 提交投稿
- `GET /api/v1/admin/submission/my` - 获取我的投稿
- `GET /api/v1/admin/submission/statistics` - 获取投稿统计

### 状态管理API
- `PUT /api/v1/admin/submission/status` - 更新投稿状态
- `PUT /api/v1/admin/submission/schedule` - 安排投稿发布
- `PUT /api/v1/admin/submission/publish` - 发布投稿
- `PUT /api/v1/admin/submission/withdraw` - 撤回投稿
- `GET /api/v1/admin/submission/logs` - 获取投稿操作日志
- `PUT /api/v1/admin/submission/batch-status` - 批量更新状态

### 文件管理API
- `POST /api/v1/admin/submission/file/upload` - 上传文件
- `GET /api/v1/admin/submission/file/list` - 获取文件列表
- `DELETE /api/v1/admin/submission/file/delete` - 删除文件
- `GET /api/v1/admin/submission/file/download` - 下载文件
- `PUT /api/v1/admin/submission/file/sort` - 更新文件排序
- `GET /api/v1/admin/submission/file/statistics` - 获取文件统计

### 分类管理API
- `GET /api/v1/admin/submission/category/list` - 获取分类列表
- `GET /api/v1/admin/submission/category/active` - 获取启用分类
- `POST /api/v1/admin/submission/category/create` - 创建分类
- `PUT /api/v1/admin/submission/category/update` - 更新分类
- `DELETE /api/v1/admin/submission/category/delete` - 删除分类
- `PUT /api/v1/admin/submission/category/sort` - 更新分类排序

### 发布日历API
- `GET /api/v1/admin/submission/calendar` - 获取发布日历
- `PUT /api/v1/admin/submission/calendar/update` - 更新发布日历
- `GET /api/v1/admin/submission/calendar/available` - 获取可用发布日期
- `GET /api/v1/admin/submission/calendar/statistics` - 获取日历统计
- `PUT /api/v1/admin/submission/calendar/holiday` - 设置节假日
- `GET /api/v1/admin/submission/calendar/submissions` - 获取指定日期投稿

## 前端页面结构

### 管理员端
- `/submission/list` - 投稿列表管理
- `/submission/calendar` - 发布日历管理
- `/submission/category` - 分类管理
- `/submission/statistics` - 统计分析

### 用户端
- `/my-submission/list` - 我的投稿列表
- `/my-submission/create` - 新建投稿
- `/my-submission/calendar` - 发布日历查看

## 权限控制

### 角色定义
- **超级管理员** (`R_SUPER`): 完整的系统管理权限
- **管理员** (`R_ADMIN`): 投稿审核和管理权限
- **普通用户** (`R_USER`): 投稿创建和管理自己的投稿

### 权限矩阵
| 功能 | 超级管理员 | 管理员 | 普通用户 |
|------|------------|--------|----------|
| 查看所有投稿 | ✅ | ✅ | ❌ |
| 审核投稿 | ✅ | ✅ | ❌ |
| 发布管理 | ✅ | ✅ | ❌ |
| 分类管理 | ✅ | ✅ | ❌ |
| 日历管理 | ✅ | ✅ | ❌ |
| 创建投稿 | ✅ | ✅ | ✅ |
| 管理自己的投稿 | ✅ | ✅ | ✅ |
| 查看发布日历 | ✅ | ✅ | ✅ |

## 下一阶段计划

### 第三阶段：高级功能开发
1. **统计分析功能**
   - 投稿数量趋势分析
   - 用户活跃度统计
   - 发布效果分析
   - 数据报表导出

2. **通知系统**
   - 状态变化通知
   - 邮件通知
   - 站内消息

3. **高级搜索和筛选**
   - 多条件组合搜索
   - 保存搜索条件
   - 快速筛选器

4. **批量操作增强**
   - 批量导入投稿
   - 批量分配分类
   - 批量设置发布时间

### 第四阶段：优化和测试
1. **性能优化**
   - 数据库查询优化
   - 文件上传优化
   - 前端渲染优化

2. **单元测试和集成测试**
   - 后端API测试
   - 前端组件测试
   - 端到端测试

3. **用户体验优化**
   - 响应式设计优化
   - 交互体验改进
   - 错误处理优化

4. **部署和上线**
   - Docker容器化
   - CI/CD流程
   - 生产环境部署

## 项目特点

1. **完全基于现有架构**: 无缝集成到现有的 `art-design-pro-edge` 系统
2. **完整的权限控制**: 细粒度的角色和状态权限管理
3. **灵活的状态管理**: 支持复杂的审核流程和状态流转
4. **文件管理支持**: 多媒体文件上传、预览和管理
5. **可扩展设计**: 为后续功能预留了扩展空间
6. **用户体验优先**: 基于成熟的UI组件库，提供一致的用户体验

## 开发规范

### 代码规范
- 遵循现有项目的代码风格
- 使用TypeScript进行类型检查
- 组件命名采用PascalCase
- API接口采用RESTful设计

### 提交规范
- feat: 新功能
- fix: 修复bug
- docs: 文档更新
- style: 代码格式调整
- refactor: 代码重构
- test: 测试相关
- chore: 构建过程或辅助工具的变动

### 文件组织
- 按功能模块组织文件结构
- 公共组件放在 `components` 目录
- 页面组件放在 `views` 目录
- API接口放在 `api` 目录
- 类型定义放在 `types` 目录

---

**开发进度**: 第二阶段已完成 ✅  
**下一阶段**: 第三阶段 - 高级功能开发  
**预计完成时间**: 根据实际开发进度调整
