package system

import (
	"errors"
	"time"

	"gorm.io/gorm"
)

// 投稿相关数据访问层

// AddSubmission 创建投稿
func AddSubmission(submission *Submission) error {
	db := GetDB()
	return db.Create(submission).Error
}

// GetSubmission 根据ID获取投稿详情
func GetSubmission(submission *Submission) error {
	db := GetDB()
	return db.Preload("User").Preload("Files").Preload("Categories").First(submission, submission.ID).Error
}

// UpdateSubmission 更新投稿
func UpdateSubmission(submission *Submission) error {
	db := GetDB()
	return db.Save(submission).Error
}

// DeleteSubmission 删除投稿
func DeleteSubmission(submission *Submission) error {
	db := GetDB()
	return db.Delete(submission).Error
}

// FindSubmissionList 分页查询投稿列表
func FindSubmissionList(submission *Submission, page, pageSize int) ([]Submission, int64, error) {
	db := GetDB()
	var submissions []Submission
	var total int64

	query := db.Model(&Submission{}).Preload("User").Preload("Categories")

	// 构建查询条件
	if submission.UserID != 0 {
		query = query.Where("user_id = ?", submission.UserID)
	}
	if submission.Status != "" {
		query = query.Where("status = ?", submission.Status)
	}
	if submission.Type != "" {
		query = query.Where("type = ?", submission.Type)
	}
	if submission.Title != "" {
		query = query.Where("title ILIKE ?", "%"+submission.Title+"%")
	}

	// 获取总数
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	if page > 0 && pageSize > 0 {
		offset := (page - 1) * pageSize
		query = query.Offset(offset).Limit(pageSize)
	}

	err = query.Order("created_at DESC").Find(&submissions).Error
	return submissions, total, err
}

// UpdateSubmissionStatus 更新投稿状态
func UpdateSubmissionStatus(submissionID uint, oldStatus, newStatus SubmissionStatus, userID uint, comment string) error {
	db := GetDB()
	return db.Transaction(func(tx *gorm.DB) error {
		// 更新投稿状态
		submission := Submission{}
		err := tx.First(&submission, submissionID).Error
		if err != nil {
			return err
		}

		submission.Status = newStatus
		now := time.Now()

		// 根据状态更新相应的时间字段
		switch newStatus {
		case SubmissionStatusSubmitted:
			submission.SubmittedAt = &now
		case SubmissionStatusApproved, SubmissionStatusRejected:
			submission.ReviewedAt = &now
		case SubmissionStatusPublished:
			submission.PublishedAt = &now
		}

		err = tx.Save(&submission).Error
		if err != nil {
			return err
		}

		// 记录操作日志
		log := SubmissionLog{
			SubmissionID: submissionID,
			UserID:       userID,
			Action:       "status_change",
			OldStatus:    oldStatus,
			NewStatus:    newStatus,
			Comment:      comment,
		}
		return tx.Create(&log).Error
	})
}

// GetSubmissionsByDateRange 根据日期范围获取投稿
func GetSubmissionsByDateRange(startDate, endDate time.Time, status SubmissionStatus) ([]Submission, error) {
	db := GetDB()
	var submissions []Submission

	query := db.Model(&Submission{}).Preload("User")
	if status != "" {
		query = query.Where("status = ?", status)
	}

	err := query.Where("publish_date BETWEEN ? AND ?", startDate, endDate).
		Order("publish_date ASC").
		Find(&submissions).Error

	return submissions, err
}

// GetSubmissionStatistics 获取投稿统计信息
func GetSubmissionStatistics(userID uint) (map[string]interface{}, error) {
	db := GetDB()
	stats := make(map[string]interface{})

	query := db.Model(&Submission{})
	if userID != 0 {
		query = query.Where("user_id = ?", userID)
	}

	// 总投稿数
	var total int64
	err := query.Count(&total).Error
	if err != nil {
		return nil, err
	}
	stats["total"] = total

	// 各状态统计
	statusStats := make(map[string]int64)
	statuses := []SubmissionStatus{
		SubmissionStatusDraft, SubmissionStatusSubmitted, SubmissionStatusReviewing,
		SubmissionStatusApproved, SubmissionStatusRejected, SubmissionStatusRevision,
		SubmissionStatusScheduled, SubmissionStatusPublished, SubmissionStatusWithdrawn,
	}

	for _, status := range statuses {
		var count int64
		statusQuery := db.Model(&Submission{})
		if userID != 0 {
			statusQuery = statusQuery.Where("user_id = ?", userID)
		}
		err := statusQuery.Where("status = ?", status).Count(&count).Error
		if err != nil {
			return nil, err
		}
		statusStats[string(status)] = count
	}
	stats["status_stats"] = statusStats

	// 本月投稿数
	now := time.Now()
	startOfMonth := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	endOfMonth := startOfMonth.AddDate(0, 1, -1)

	var monthlyCount int64
	monthlyQuery := db.Model(&Submission{})
	if userID != 0 {
		monthlyQuery = monthlyQuery.Where("user_id = ?", userID)
	}
	err = monthlyQuery.Where("created_at BETWEEN ? AND ?", startOfMonth, endOfMonth).Count(&monthlyCount).Error
	if err != nil {
		return nil, err
	}
	stats["monthly_count"] = monthlyCount

	return stats, nil
}

// BatchDeleteSubmissions 批量删除投稿
func BatchDeleteSubmissions(submissionIDs []uint, userID uint) error {
	db := GetDB()
	return db.Transaction(func(tx *gorm.DB) error {
		// 检查权限和状态
		var submissions []Submission
		err := tx.Where("id IN ?", submissionIDs).Find(&submissions).Error
		if err != nil {
			return err
		}

		for _, submission := range submissions {
			// 只有草稿状态的投稿可以被删除，或者管理员可以删除任何状态的投稿
			if submission.Status != SubmissionStatusDraft && submission.UserID != userID {
				return errors.New("只能删除草稿状态的投稿")
			}
		}

		// 删除相关文件记录
		err = tx.Where("submission_id IN ?", submissionIDs).Delete(&SubmissionFile{}).Error
		if err != nil {
			return err
		}

		// 删除操作日志
		err = tx.Where("submission_id IN ?", submissionIDs).Delete(&SubmissionLog{}).Error
		if err != nil {
			return err
		}

		// 删除投稿
		return tx.Where("id IN ?", submissionIDs).Delete(&Submission{}).Error
	})
}

// GetSubmissionLogs 获取投稿操作日志
func GetSubmissionLogs(submissionID uint) ([]SubmissionLog, error) {
	db := GetDB()
	var logs []SubmissionLog
	err := db.Where("submission_id = ?", submissionID).
		Preload("User").
		Order("created_at DESC").
		Find(&logs).Error
	return logs, err
}
