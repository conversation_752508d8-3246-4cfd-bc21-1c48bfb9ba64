package system

import (
	"time"
	"gorm.io/gorm"
)

// Department 部门表
type SystemDepartment struct {
	gorm.Model
	Name        string       `json:"name,omitempty"`
	Sort        uint         `json:"sort,omitempty"`
	Status      uint         `json:"status,omitempty"`                               // 状态(1:启用 2:禁用)
	SystemUsers []SystemUser `json:"users,omitempty" gorm:"foreignKey:DepartmentID"` // 一对多关联用户表
}

// Role 角色表
type SystemRole struct {
	gorm.Model
	Name            string           `json:"name,omitempty"`
	Desc            string           `json:"desc,omitempty"`
	Status          uint             `json:"status,omitempty"`                                                  // 状态(1:启用 2:禁用)
	SystemMenus     []SystemMenu     `json:"menus,omitempty" gorm:"many2many:system_roles__system_menus;"`      // 多对多关联菜单表
	SystemUsers     []SystemUser     `json:"users,omitempty" gorm:"foreignKey:RoleID"`                          // 一对多关联用户表
	SystemMenuAuths []SystemMenuAuth `json:"menu_auths,omitempty" gorm:"many2many:system_roles__system_auths;"` // 多对多关联菜单按钮权限表
}

// Menu 菜单表
type SystemMenu struct {
	gorm.Model
	Path            string           `json:"path,omitempty"`
	Name            string           `json:"name,omitempty"`
	Component       string           `json:"component,omitempty"`            // vue组件
	Title           string           `json:"title,omitempty"`                // 菜单标题
	Icon            string           `json:"icon,omitempty"`                 // 菜单图标
	ShowBadge       uint             `json:"show_badge,omitempty"`           // 是否显示角标(1:显示 2:隐藏)
	ShowTextBadge   string           `json:"show_text_badge,omitempty"`      // 是否显示文本角标(1:显示 2:隐藏)
	IsHide          uint             `json:"is_hide,omitempty"`              // 是否隐藏(1:隐藏 2:显示)
	IsHideTab       uint             `json:"is_hide_tab,omitempty"`          // 是否隐藏标签(1:隐藏 2:显示)
	Link            string           `json:"link,omitempty"`                 // 链接(外链)
	IsIframe        uint             `json:"is_iframe,omitempty"`            // 是否内嵌(1:内嵌 2:不内嵌)
	KeepAlive       uint             `json:"keep_alive,omitempty"`           // 是否缓存(1:缓存 2:不缓存)
	IsFirstLevel    uint             `json:"is_in_main_container,omitempty"` // 是否在主容器内(一级菜单使用)(1:是 2:否)
	Status          uint             `json:"status,omitempty"`               // 状态(1:启用 2:禁用)
	Level           uint             `json:"level,omitempty"`                // 层级(从1开始)
	ParentID        uint             `json:"parent_id,omitempty"`            // 父级ID
	Sort            uint             `json:"sort,omitempty"`                 // 排序(从大到小)
	SystemRoles     []SystemRole     `json:"roles,omitempty" gorm:"many2many:system_roles__system_menus;"`
	SystemMenuAuths []SystemMenuAuth `json:"menu_auths,omitempty" gorm:"foreignKey:MenuID"` // 一对多关联菜单按钮权限表
}

// MenuPermission 菜单按钮权限表
type SystemMenuAuth struct {
	gorm.Model
	MenuID      uint         `json:"menu_id,omitempty"`
	Mark        string       `json:"mark,omitempty"` // 标识
	Title       string       `json:"title,omitempty"`
	SystemRoles []SystemRole `json:"roles,omitempty" gorm:"many2many:system_roles__system_auths;"` // 多对多关联角色表
}

// User 用户表
type SystemUser struct {
	gorm.Model
	DepartmentID uint   `json:"department_id,omitempty"`
	RoleID       uint   `json:"role_id,omitempty"`
	Name         string `json:"name,omitempty"`     // 昵称
	Username     string `json:"username,omitempty"` // 姓名, 不可修改
	Password     string `json:"password,omitempty"`
	Phone        string `json:"phone,omitempty"`
	Gender       uint   `json:"gender,omitempty"` // 性别(1:男 2:女)
	Status       uint   `json:"status,omitempty"` // 状态(1:启用 2:禁用)
}

type SystemUserLoginLog struct {
	gorm.Model
	UserName string `json:"user_name,omitempty"`
	Password string `json:"password,omitempty"`
	IP       string `json:"ip,omitempty"`
}

// 投稿状态枚举
type SubmissionStatus string

const (
	SubmissionStatusDraft     SubmissionStatus = "draft"      // 草稿
	SubmissionStatusSubmitted SubmissionStatus = "submitted"  // 已提交
	SubmissionStatusReviewing SubmissionStatus = "reviewing"  // 审核中
	SubmissionStatusApproved  SubmissionStatus = "approved"   // 已通过
	SubmissionStatusRejected  SubmissionStatus = "rejected"   // 已拒绝
	SubmissionStatusRevision  SubmissionStatus = "revision"   // 需修改
	SubmissionStatusScheduled SubmissionStatus = "scheduled"  // 待发布
	SubmissionStatusPublished SubmissionStatus = "published"  // 已发布
	SubmissionStatusWithdrawn SubmissionStatus = "withdrawn"  // 已撤回
)

// 投稿类型枚举
type SubmissionType string

const (
	SubmissionTypeArticle SubmissionType = "article" // 图文文章
	SubmissionTypeVideo   SubmissionType = "video"   // 视频
	SubmissionTypeMixed   SubmissionType = "mixed"   // 混合媒体
)

// 投稿优先级枚举
type SubmissionPriority string

const (
	SubmissionPriorityLow    SubmissionPriority = "low"    // 低优先级
	SubmissionPriorityNormal SubmissionPriority = "normal" // 普通优先级
	SubmissionPriorityHigh   SubmissionPriority = "high"   // 高优先级
	SubmissionPriorityUrgent SubmissionPriority = "urgent" // 紧急
)

// 投稿表
type Submission struct {
	gorm.Model
	UserID       uint               `json:"user_id" gorm:"not null;index"`                    // 投稿用户ID
	Title        string             `json:"title" gorm:"not null;size:255"`                   // 投稿标题
	Description  string             `json:"description" gorm:"type:text"`                     // 投稿描述
	Type         SubmissionType     `json:"type" gorm:"not null;default:'article'"`           // 投稿类型
	Status       SubmissionStatus   `json:"status" gorm:"not null;default:'draft';index"`     // 投稿状态
	Priority     SubmissionPriority `json:"priority" gorm:"not null;default:'normal'"`        // 优先级
	PublishDate  *time.Time         `json:"publish_date" gorm:"index"`                        // 计划发布日期
	Tags         string             `json:"tags" gorm:"size:500"`                             // 标签，逗号分隔
	Keywords     string             `json:"keywords" gorm:"size:500"`                         // 关键词，逗号分隔
	ViewCount    uint               `json:"view_count" gorm:"default:0"`                      // 查看次数
	AdminNotes   string             `json:"admin_notes" gorm:"type:text"`                     // 管理员备注
	SubmittedAt  *time.Time         `json:"submitted_at" gorm:"index"`                        // 提交时间
	ReviewedAt   *time.Time         `json:"reviewed_at"`                                      // 审核时间
	PublishedAt  *time.Time         `json:"published_at"`                                     // 发布时间

	// 关联关系
	User            SystemUser        `json:"user,omitempty" gorm:"foreignKey:UserID"`
	Files           []SubmissionFile  `json:"files,omitempty" gorm:"foreignKey:SubmissionID"`
	Logs            []SubmissionLog   `json:"logs,omitempty" gorm:"foreignKey:SubmissionID"`
	Categories      []Category        `json:"categories,omitempty" gorm:"many2many:submission_categories;"`
}

// 投稿文件表
type SubmissionFile struct {
	gorm.Model
	SubmissionID uint   `json:"submission_id" gorm:"not null;index"`           // 投稿ID
	OriginalName string `json:"original_name" gorm:"not null;size:255"`        // 原始文件名
	StoredName   string `json:"stored_name" gorm:"not null;size:255"`          // 存储文件名
	FilePath     string `json:"file_path" gorm:"not null;size:500"`            // 文件路径
	FileType     string `json:"file_type" gorm:"not null;size:50"`             // 文件类型
	FileSize     int64  `json:"file_size" gorm:"not null"`                     // 文件大小（字节）
	MimeType     string `json:"mime_type" gorm:"not null;size:100"`            // MIME类型
	FileCategory string `json:"file_category" gorm:"not null;size:50"`         // 文件分类（image/video/document）
	Description  string `json:"description" gorm:"type:text"`                  // 文件描述
	SortOrder    uint   `json:"sort_order" gorm:"default:0"`                   // 排序

	// 关联关系
	Submission Submission `json:"submission,omitempty" gorm:"foreignKey:SubmissionID"`
}

// 投稿操作日志表
type SubmissionLog struct {
	gorm.Model
	SubmissionID uint             `json:"submission_id" gorm:"not null;index"`     // 投稿ID
	UserID       uint             `json:"user_id" gorm:"not null;index"`           // 操作用户ID
	Action       string           `json:"action" gorm:"not null;size:50"`          // 操作类型
	OldStatus    SubmissionStatus `json:"old_status" gorm:"size:20"`               // 原状态
	NewStatus    SubmissionStatus `json:"new_status" gorm:"size:20"`               // 新状态
	Comment      string           `json:"comment" gorm:"type:text"`                // 操作备注

	// 关联关系
	Submission Submission `json:"submission,omitempty" gorm:"foreignKey:SubmissionID"`
	User       SystemUser `json:"user,omitempty" gorm:"foreignKey:UserID"`
}

// 分类表
type Category struct {
	gorm.Model
	Name        string `json:"name" gorm:"not null;size:100;uniqueIndex"`  // 分类名称
	Description string `json:"description" gorm:"type:text"`               // 分类描述
	SortOrder   uint   `json:"sort_order" gorm:"default:0"`                // 排序
	IsActive    bool   `json:"is_active" gorm:"default:true"`              // 是否启用

	// 关联关系
	Submissions []Submission `json:"submissions,omitempty" gorm:"many2many:submission_categories;"`
}

// 发布日历表
type PublishCalendar struct {
	gorm.Model
	PublishDate        time.Time `json:"publish_date" gorm:"not null;uniqueIndex;type:date"`  // 发布日期
	MaxSubmissions     uint      `json:"max_submissions" gorm:"default:5"`                    // 最大投稿数量
	CurrentSubmissions uint      `json:"current_submissions" gorm:"default:0"`               // 当前投稿数量
	Notes              string    `json:"notes" gorm:"type:text"`                             // 备注
	IsHoliday          bool      `json:"is_holiday" gorm:"default:false"`                    // 是否节假日
}
