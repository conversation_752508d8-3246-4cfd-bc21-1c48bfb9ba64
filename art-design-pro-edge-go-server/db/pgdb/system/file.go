package system

import (
	"gorm.io/gorm"
)

// 文件相关数据访问层

// AddSubmissionFile 添加投稿文件
func AddSubmissionFile(file *SubmissionFile) error {
	db := GetDB()
	return db.Create(file).Error
}

// GetSubmissionFile 根据ID获取文件信息
func GetSubmissionFile(file *SubmissionFile) error {
	db := GetDB()
	return db.First(file, file.ID).Error
}

// UpdateSubmissionFile 更新文件信息
func UpdateSubmissionFile(file *SubmissionFile) error {
	db := GetDB()
	return db.Save(file).Error
}

// DeleteSubmissionFile 删除文件记录
func DeleteSubmissionFile(file *SubmissionFile) error {
	db := GetDB()
	return db.Delete(file).Error
}

// FindSubmissionFilesBySubmissionID 根据投稿ID获取文件列表
func FindSubmissionFilesBySubmissionID(submissionID uint) ([]SubmissionFile, error) {
	db := GetDB()
	var files []SubmissionFile
	err := db.Where("submission_id = ?", submissionID).
		Order("sort_order ASC, created_at ASC").
		Find(&files).Error
	return files, err
}

// FindSubmissionFileList 分页查询文件列表
func FindSubmissionFileList(file *SubmissionFile, page, pageSize int) ([]SubmissionFile, int64, error) {
	db := GetDB()
	var files []SubmissionFile
	var total int64

	query := db.Model(&SubmissionFile{}).Preload("Submission")

	// 构建查询条件
	if file.SubmissionID != 0 {
		query = query.Where("submission_id = ?", file.SubmissionID)
	}
	if file.FileType != "" {
		query = query.Where("file_type = ?", file.FileType)
	}
	if file.FileCategory != "" {
		query = query.Where("file_category = ?", file.FileCategory)
	}
	if file.OriginalName != "" {
		query = query.Where("original_name ILIKE ?", "%"+file.OriginalName+"%")
	}

	// 获取总数
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	if page > 0 && pageSize > 0 {
		offset := (page - 1) * pageSize
		query = query.Offset(offset).Limit(pageSize)
	}

	err = query.Order("created_at DESC").Find(&files).Error
	return files, total, err
}

// BatchDeleteSubmissionFiles 批量删除文件
func BatchDeleteSubmissionFiles(fileIDs []uint) error {
	db := GetDB()
	return db.Where("id IN ?", fileIDs).Delete(&SubmissionFile{}).Error
}

// UpdateFilesSortOrder 更新文件排序
func UpdateFilesSortOrder(fileOrders []struct {
	ID        uint `json:"id"`
	SortOrder uint `json:"sort_order"`
}) error {
	db := GetDB()
	return db.Transaction(func(tx *gorm.DB) error {
		for _, order := range fileOrders {
			err := tx.Model(&SubmissionFile{}).
				Where("id = ?", order.ID).
				Update("sort_order", order.SortOrder).Error
			if err != nil {
				return err
			}
		}
		return nil
	})
}

// GetFileStatistics 获取文件统计信息
func GetFileStatistics() (map[string]interface{}, error) {
	db := GetDB()
	stats := make(map[string]interface{})

	// 总文件数
	var total int64
	err := db.Model(&SubmissionFile{}).Count(&total).Error
	if err != nil {
		return nil, err
	}
	stats["total"] = total

	// 按文件类型统计
	var typeStats []struct {
		FileCategory string `json:"file_category"`
		Count        int64  `json:"count"`
	}
	err = db.Model(&SubmissionFile{}).
		Select("file_category, count(*) as count").
		Group("file_category").
		Find(&typeStats).Error
	if err != nil {
		return nil, err
	}
	stats["type_stats"] = typeStats

	// 总文件大小
	var totalSize int64
	err = db.Model(&SubmissionFile{}).
		Select("COALESCE(SUM(file_size), 0)").
		Scan(&totalSize).Error
	if err != nil {
		return nil, err
	}
	stats["total_size"] = totalSize

	return stats, nil
}

// GetFilesBySubmissionIDs 根据投稿ID列表获取文件
func GetFilesBySubmissionIDs(submissionIDs []uint) (map[uint][]SubmissionFile, error) {
	db := GetDB()
	var files []SubmissionFile
	err := db.Where("submission_id IN ?", submissionIDs).
		Order("submission_id ASC, sort_order ASC").
		Find(&files).Error
	if err != nil {
		return nil, err
	}

	// 按投稿ID分组
	fileMap := make(map[uint][]SubmissionFile)
	for _, file := range files {
		fileMap[file.SubmissionID] = append(fileMap[file.SubmissionID], file)
	}

	return fileMap, nil
}

// CheckFileExists 检查文件是否存在
func CheckFileExists(filePath string) (bool, error) {
	db := GetDB()
	var count int64
	err := db.Model(&SubmissionFile{}).
		Where("file_path = ?", filePath).
		Count(&count).Error
	return count > 0, err
}

// GetFilesByCategory 根据文件分类获取文件列表
func GetFilesByCategory(category string, limit int) ([]SubmissionFile, error) {
	db := GetDB()
	var files []SubmissionFile
	query := db.Where("file_category = ?", category).
		Order("created_at DESC")
	
	if limit > 0 {
		query = query.Limit(limit)
	}
	
	err := query.Find(&files).Error
	return files, err
}

// CleanupOrphanFiles 清理孤立文件（没有关联投稿的文件）
func CleanupOrphanFiles() ([]SubmissionFile, error) {
	db := GetDB()
	var orphanFiles []SubmissionFile
	
	// 查找孤立文件
	err := db.Where("submission_id NOT IN (SELECT id FROM submissions)").
		Find(&orphanFiles).Error
	if err != nil {
		return nil, err
	}
	
	// 删除孤立文件记录
	if len(orphanFiles) > 0 {
		var fileIDs []uint
		for _, file := range orphanFiles {
			fileIDs = append(fileIDs, file.ID)
		}
		err = db.Where("id IN ?", fileIDs).Delete(&SubmissionFile{}).Error
		if err != nil {
			return nil, err
		}
	}
	
	return orphanFiles, nil
}
