package system

import (
	"time"
	"gorm.io/gorm"
)

// 分类相关数据访问层

// AddCategory 创建分类
func AddCategory(category *Category) error {
	db := GetDB()
	return db.Create(category).Error
}

// GetCategory 根据ID获取分类详情
func GetCategory(category *Category) error {
	db := GetDB()
	return db.First(category, category.ID).Error
}

// UpdateCategory 更新分类
func UpdateCategory(category *Category) error {
	db := GetDB()
	return db.Save(category).Error
}

// DeleteCategory 删除分类
func DeleteCategory(category *Category) error {
	db := GetDB()
	return db.Delete(category).Error
}

// FindCategoryList 分页查询分类列表
func FindCategoryList(category *Category, page, pageSize int) ([]Category, int64, error) {
	db := GetDB()
	var categories []Category
	var total int64

	query := db.Model(&Category{})

	// 构建查询条件
	if category.Name != "" {
		query = query.Where("name ILIKE ?", "%"+category.Name+"%")
	}
	if category.IsActive {
		query = query.Where("is_active = ?", category.IsActive)
	}

	// 获取总数
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	if page > 0 && pageSize > 0 {
		offset := (page - 1) * pageSize
		query = query.Offset(offset).Limit(pageSize)
	}

	err = query.Order("sort_order ASC, created_at DESC").Find(&categories).Error
	return categories, total, err
}

// GetAllActiveCategories 获取所有启用的分类
func GetAllActiveCategories() ([]Category, error) {
	db := GetDB()
	var categories []Category
	err := db.Where("is_active = ?", true).
		Order("sort_order ASC, created_at DESC").
		Find(&categories).Error
	return categories, err
}

// UpdateCategorySortOrder 更新分类排序
func UpdateCategorySortOrder(categoryOrders []struct {
	ID        uint `json:"id"`
	SortOrder uint `json:"sort_order"`
}) error {
	db := GetDB()
	return db.Transaction(func(tx *gorm.DB) error {
		for _, order := range categoryOrders {
			err := tx.Model(&Category{}).
				Where("id = ?", order.ID).
				Update("sort_order", order.SortOrder).Error
			if err != nil {
				return err
			}
		}
		return nil
	})
}

// 发布日历相关数据访问层

// AddPublishCalendar 创建发布日历记录
func AddPublishCalendar(calendar *PublishCalendar) error {
	db := GetDB()
	return db.Create(calendar).Error
}

// GetPublishCalendar 根据日期获取发布日历
func GetPublishCalendar(date time.Time) (*PublishCalendar, error) {
	db := GetDB()
	var calendar PublishCalendar
	err := db.Where("publish_date = ?", date.Format("2006-01-02")).First(&calendar).Error
	if err != nil {
		return nil, err
	}
	return &calendar, nil
}

// UpdatePublishCalendar 更新发布日历
func UpdatePublishCalendar(calendar *PublishCalendar) error {
	db := GetDB()
	return db.Save(calendar).Error
}

// GetPublishCalendarByDateRange 根据日期范围获取发布日历
func GetPublishCalendarByDateRange(startDate, endDate time.Time) ([]PublishCalendar, error) {
	db := GetDB()
	var calendars []PublishCalendar
	err := db.Where("publish_date BETWEEN ? AND ?", 
		startDate.Format("2006-01-02"), 
		endDate.Format("2006-01-02")).
		Order("publish_date ASC").
		Find(&calendars).Error
	return calendars, err
}

// GetOrCreatePublishCalendar 获取或创建发布日历记录
func GetOrCreatePublishCalendar(date time.Time) (*PublishCalendar, error) {
	db := GetDB()
	var calendar PublishCalendar
	
	err := db.Where("publish_date = ?", date.Format("2006-01-02")).First(&calendar).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// 创建新记录
			calendar = PublishCalendar{
				PublishDate:        date,
				MaxSubmissions:     5, // 默认每日最大投稿数
				CurrentSubmissions: 0,
				IsHoliday:          false,
			}
			err = db.Create(&calendar).Error
			if err != nil {
				return nil, err
			}
		} else {
			return nil, err
		}
	}
	
	return &calendar, nil
}

// UpdateCalendarSubmissionCount 更新日历的投稿数量
func UpdateCalendarSubmissionCount(date time.Time, increment bool) error {
	db := GetDB()
	
	calendar, err := GetOrCreatePublishCalendar(date)
	if err != nil {
		return err
	}
	
	if increment {
		calendar.CurrentSubmissions++
	} else {
		if calendar.CurrentSubmissions > 0 {
			calendar.CurrentSubmissions--
		}
	}
	
	return db.Save(calendar).Error
}

// GetAvailablePublishDates 获取可用的发布日期
func GetAvailablePublishDates(startDate, endDate time.Time) ([]time.Time, error) {
	db := GetDB()
	var calendars []PublishCalendar
	
	err := db.Where("publish_date BETWEEN ? AND ? AND current_submissions < max_submissions AND is_holiday = ?", 
		startDate.Format("2006-01-02"), 
		endDate.Format("2006-01-02"),
		false).
		Order("publish_date ASC").
		Find(&calendars).Error
	
	if err != nil {
		return nil, err
	}
	
	var availableDates []time.Time
	for _, calendar := range calendars {
		availableDates = append(availableDates, calendar.PublishDate)
	}
	
	return availableDates, nil
}

// SetHolidayDates 设置节假日
func SetHolidayDates(dates []time.Time, isHoliday bool) error {
	db := GetDB()
	return db.Transaction(func(tx *gorm.DB) error {
		for _, date := range dates {
			calendar, err := GetOrCreatePublishCalendar(date)
			if err != nil {
				return err
			}
			calendar.IsHoliday = isHoliday
			err = tx.Save(calendar).Error
			if err != nil {
				return err
			}
		}
		return nil
	})
}

// GetCalendarStatistics 获取日历统计信息
func GetCalendarStatistics(startDate, endDate time.Time) (map[string]interface{}, error) {
	db := GetDB()
	stats := make(map[string]interface{})
	
	// 总天数
	totalDays := int(endDate.Sub(startDate).Hours()/24) + 1
	stats["total_days"] = totalDays
	
	// 工作日数量（非节假日）
	var workDays int64
	err := db.Model(&PublishCalendar{}).
		Where("publish_date BETWEEN ? AND ? AND is_holiday = ?", 
			startDate.Format("2006-01-02"), 
			endDate.Format("2006-01-02"), 
			false).
		Count(&workDays).Error
	if err != nil {
		return nil, err
	}
	stats["work_days"] = workDays
	
	// 节假日数量
	var holidays int64
	err = db.Model(&PublishCalendar{}).
		Where("publish_date BETWEEN ? AND ? AND is_holiday = ?", 
			startDate.Format("2006-01-02"), 
			endDate.Format("2006-01-02"), 
			true).
		Count(&holidays).Error
	if err != nil {
		return nil, err
	}
	stats["holidays"] = holidays
	
	// 已安排投稿的天数
	var scheduledDays int64
	err = db.Model(&PublishCalendar{}).
		Where("publish_date BETWEEN ? AND ? AND current_submissions > 0", 
			startDate.Format("2006-01-02"), 
			endDate.Format("2006-01-02")).
		Count(&scheduledDays).Error
	if err != nil {
		return nil, err
	}
	stats["scheduled_days"] = scheduledDays
	
	// 总计划投稿数
	var totalScheduled int64
	err = db.Model(&PublishCalendar{}).
		Where("publish_date BETWEEN ? AND ?", 
			startDate.Format("2006-01-02"), 
			endDate.Format("2006-01-02")).
		Select("COALESCE(SUM(current_submissions), 0)").
		Scan(&totalScheduled).Error
	if err != nil {
		return nil, err
	}
	stats["total_scheduled"] = totalScheduled
	
	return stats, nil
}
