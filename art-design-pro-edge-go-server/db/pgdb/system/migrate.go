package system

import (
	"fmt"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"api-server/config"
)

func migrateTable(db *gorm.DB) error {
	err := db.AutoMigrate(
		&SystemDepartment{},
		&SystemRole{},
		&SystemMenu{},
		&SystemMenuAuth{},
		&SystemUser{},
		&SystemUserLoginLog{},
		// 新媒体投稿系统相关表
		&Submission{},
		&SubmissionFile{},
		&SubmissionLog{},
		&Category{},
		&PublishCalendar{},
	)
	if err != nil {
		zap.L().Error("failed to migrate system model", zap.Error(err))
		return err
	}
	return nil
}

func migrateData(db *gorm.DB) error {
	err := db.Transaction(func(tx *gorm.DB) error {
		// 检查是否已有数据，如果有则跳过初始化
		var count int64
		tx.Model(&SystemMenu{}).Count(&count)
		if count > 0 {
			zap.L().Info("menu data already exists, skipping initial data creation")
			return nil
		}

		// 创建菜单
		menus := []SystemMenu{
			{Model: gorm.Model{ID: 1}, Path: "/dashboard", Name: "Dashboard", Component: "/index/index", Title: "仪表盘", Icon: "&#xe721;", KeepAlive: 2, Status: 1, Level: 1, ParentID: 0, Sort: 99},
			{Model: gorm.Model{ID: 2}, Path: "/system", Name: "System", Component: "/index/index", Title: "系统管理", Icon: "&#xe72b;", KeepAlive: 2, Status: 1, Level: 1, ParentID: 0, Sort: 20},
			{Model: gorm.Model{ID: 3}, Path: "menu", Name: "SystemMenu", Component: "/system/menu/index", Title: "菜单管理", KeepAlive: 2, Status: 1, Level: 2, ParentID: 2, Sort: 99},
			{Model: gorm.Model{ID: 4}, Path: "role", Name: "SystemRole", Component: "/system/role/index", Title: "角色管理", KeepAlive: 2, Status: 1, Level: 2, ParentID: 2, Sort: 88},
			{Model: gorm.Model{ID: 5}, Path: "department", Name: "SystemDepartment", Component: "/system/department/index", Title: "部门管理", KeepAlive: 2, Status: 1, Level: 2, ParentID: 2, Sort: 77},
			{Model: gorm.Model{ID: 6}, Path: "user", Name: "SystemUser", Component: "/system/user/index", Title: "用户管理", KeepAlive: 2, Status: 1, Level: 2, ParentID: 2, Sort: 66},
			{Model: gorm.Model{ID: 7}, Path: "console", Name: "DashboardConsole", Component: "/dashboard/console/index", Title: "工作台", Icon: "", KeepAlive: 2, Status: 1, Level: 2, ParentID: 1, Sort: 99},
			{Model: gorm.Model{ID: 8}, Path: "analysis", Name: "DashboardAnalysis", Component: "/dashboard/analysis/index", Title: "分析页", Icon: "", KeepAlive: 2, Status: 1, Level: 2, ParentID: 1, Sort: 88},
			{Model: gorm.Model{ID: 9}, Path: "/private", Name: "Private", Component: "/index/index", Title: "隐藏页面", Icon: "", KeepAlive: 2, Status: 1, Level: 1, ParentID: 0, Sort: 99, IsHide: 1},
			// 新媒体投稿系统菜单
			{Model: gorm.Model{ID: 10}, Path: "/submission", Name: "Submission", Component: "/index/index", Title: "投稿管理", Icon: "&#xe6b2;", KeepAlive: 2, Status: 1, Level: 1, ParentID: 0, Sort: 80},
			{Model: gorm.Model{ID: 11}, Path: "list", Name: "SubmissionList", Component: "/submission/list/index", Title: "投稿列表", KeepAlive: 2, Status: 1, Level: 2, ParentID: 10, Sort: 99},
			{Model: gorm.Model{ID: 12}, Path: "calendar", Name: "SubmissionCalendar", Component: "/submission/calendar/index", Title: "发布日历", KeepAlive: 2, Status: 1, Level: 2, ParentID: 10, Sort: 88},
			{Model: gorm.Model{ID: 13}, Path: "category", Name: "SubmissionCategory", Component: "/submission/category/index", Title: "分类管理", KeepAlive: 2, Status: 1, Level: 2, ParentID: 10, Sort: 77},
			{Model: gorm.Model{ID: 14}, Path: "statistics", Name: "SubmissionStatistics", Component: "/submission/statistics/index", Title: "统计分析", KeepAlive: 2, Status: 1, Level: 2, ParentID: 10, Sort: 66},
			// 用户端菜单
			{Model: gorm.Model{ID: 15}, Path: "/my-submission", Name: "MySubmission", Component: "/index/index", Title: "我的投稿", Icon: "&#xe6b1;", KeepAlive: 2, Status: 1, Level: 1, ParentID: 0, Sort: 70},
			{Model: gorm.Model{ID: 16}, Path: "my-list", Name: "MySubmissionList", Component: "/my-submission/list/index", Title: "投稿列表", KeepAlive: 2, Status: 1, Level: 2, ParentID: 15, Sort: 99},
			{Model: gorm.Model{ID: 17}, Path: "create", Name: "CreateSubmission", Component: "/my-submission/create/index", Title: "新建投稿", KeepAlive: 2, Status: 1, Level: 2, ParentID: 15, Sort: 88},
			{Model: gorm.Model{ID: 18}, Path: "view-calendar", Name: "ViewCalendar", Component: "/my-submission/calendar/index", Title: "发布日历", KeepAlive: 2, Status: 1, Level: 2, ParentID: 15, Sort: 77},
		}
		err := db.Create(&menus).Error
		if err != nil {
			zap.L().Error("failed to create menu", zap.Error(err))
			return err
		}

		// 检查是否已有角色数据
		tx.Model(&SystemRole{}).Count(&count)
		if count > 0 {
			zap.L().Info("role data already exists, skipping role creation")
			return nil
		}

		// 创建角色
		roles := []SystemRole{
			{Model: gorm.Model{ID: 1}, Name: "超级管理员", Desc: "拥有所有权限", Status: 1},
			{Model: gorm.Model{ID: 2}, Name: "普通用户", Desc: "普通用户", Status: 1},
		}
		err = db.Create(&roles).Error
		if err != nil {
			zap.L().Error("failed to create role", zap.Error(err))
			return err
		}

		// 为角色分配菜单权限
		// 超级管理员拥有所有菜单权限
		adminRole := SystemRole{}
		err = db.First(&adminRole, 1).Error
		if err != nil {
			zap.L().Error("failed to find admin role", zap.Error(err))
			return err
		}
		// 为超级管理员分配所有菜单
		var allMenus []SystemMenu
		err = db.Find(&allMenus).Error
		if err != nil {
			zap.L().Error("failed to find menus", zap.Error(err))
			return err
		}
		err = db.Model(&adminRole).Association("SystemMenus").Append(&allMenus)
		if err != nil {
			zap.L().Error("failed to associate menus with admin role", zap.Error(err))
			return err
		}
		// 为普通用户分配首页菜单
		normalRole := SystemRole{}
		err = db.First(&normalRole, 2).Error
		if err != nil {
			zap.L().Error("failed to find normal role", zap.Error(err))
			return err
		}
		// 为普通用户分配工作台和分析页菜单
		var consoleMenu, analysisMenu, dashboardMenu SystemMenu
		err = db.First(&dashboardMenu, 1).Error
		if err != nil {
			zap.L().Error("failed to find dashboard menu", zap.Error(err))
			return err
		}
		err = db.First(&consoleMenu, 7).Error
		if err != nil {
			zap.L().Error("failed to find console menu", zap.Error(err))
			return err
		}
		err = db.First(&analysisMenu, 8).Error
		if err != nil {
			zap.L().Error("failed to find analysis menu", zap.Error(err))
			return err
		}
		err = db.Model(&normalRole).Association("SystemMenus").Append([]SystemMenu{dashboardMenu, consoleMenu, analysisMenu})
		if err != nil {
			zap.L().Error("failed to associate console and analysis menus with normal role", zap.Error(err))
			return err
		}

		// 检查是否已有部门数据
		tx.Model(&SystemDepartment{}).Count(&count)
		if count > 0 {
			zap.L().Info("department data already exists, skipping department creation")
			return nil
		}

		// 创建部门
		departments := []SystemDepartment{
			{Model: gorm.Model{ID: 1}, Name: "管理中心", Sort: 1, Status: 1},
		}
		err = db.Create(&departments).Error
		if err != nil {
			zap.L().Error("failed to create department", zap.Error(err))
			return err
		}

		// 检查是否已有用户数据
		tx.Model(&SystemUser{}).Count(&count)
		if count > 0 {
			zap.L().Info("user data already exists, skipping user creation")
			return nil
		}

		// 创建用户
		pwd := encryptionPWD(config.AdminPassword)
		users := []SystemUser{
			{Model: gorm.Model{ID: 1}, DepartmentID: 1, RoleID: 1, Name: "超级管理员", Username: "admin", Password: pwd, Status: 1, Gender: 1},
		}
		err = db.Create(&users).Error
		if err != nil {
			zap.L().Error("failed to create user", zap.Error(err))
			return err
		}

		// 检查是否已有分类数据
		tx.Model(&Category{}).Count(&count)
		if count == 0 {
			// 创建默认分类
			categories := []Category{
				{Model: gorm.Model{ID: 1}, Name: "科技资讯", Description: "科技相关的新闻和资讯", SortOrder: 1, IsActive: true},
				{Model: gorm.Model{ID: 2}, Name: "行业动态", Description: "行业发展动态和趋势", SortOrder: 2, IsActive: true},
				{Model: gorm.Model{ID: 3}, Name: "产品介绍", Description: "产品功能和特性介绍", SortOrder: 3, IsActive: true},
				{Model: gorm.Model{ID: 4}, Name: "案例分享", Description: "成功案例和经验分享", SortOrder: 4, IsActive: true},
				{Model: gorm.Model{ID: 5}, Name: "技术教程", Description: "技术教程和操作指南", SortOrder: 5, IsActive: true},
			}
			err = db.Create(&categories).Error
			if err != nil {
				zap.L().Error("failed to create categories", zap.Error(err))
				return err
			}
			zap.L().Info("categories created successfully")
		}

		return nil
	})
	return err
}

func resetSequences(db *gorm.DB) error {
	tables := []string{
		"system_menus", "system_roles", "system_departments", "system_users",
		"system_menu_auths", // 添加这个表以确保菜单权限序列也被重置
		// 新媒体投稿系统表
		"submissions", "submission_files", "submission_logs", "categories", "publish_calendars",
	}

	for _, table := range tables {
		seqName := table + "_id_seq"
		query := fmt.Sprintf("SELECT setval('%s', (SELECT COALESCE(MAX(id), 1) FROM %s));", seqName, table)
		if err := db.Exec(query).Error; err != nil {
			zap.L().Error("failed to reset sequence", zap.String("sequence", seqName), zap.Error(err))
			return err
		}
		zap.L().Info("sequence reset successfully", zap.String("sequence", seqName))
	}
	return nil
}

func Migrate(db *gorm.DB) error {
	err := migrateTable(db)
	if err != nil {
		return err
	}
	err = migrateData(db)
	if err != nil {
		return err
	}
	// 添加序列重置操作
	err = resetSequences(db)
	if err != nil {
		return err
	}
	return nil
}
