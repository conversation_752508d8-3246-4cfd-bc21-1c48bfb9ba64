package submission

import (
	"time"

	"github.com/gin-gonic/gin"

	"api-server/api/middleware"
	"api-server/api/response"
	"api-server/db/pgdb/system"
	"api-server/util/authentication"
)

// GetPublishCalendar 获取发布日历
func GetPublishCalendar(c *gin.Context) {
	params := &struct {
		StartDate string `json:"start_date" form:"start_date" binding:"required"`
		EndDate   string `json:"end_date" form:"end_date" binding:"required"`
	}{}
	if !middleware.CheckParam(params, c) {
		return
	}

	// 解析日期
	startDate, err := time.Parse("2006-01-02", params.StartDate)
	if err != nil {
		response.ReturnError(c, response.INVALID_ARGUMENT, "开始日期格式错误")
		return
	}

	endDate, err := time.Parse("2006-01-02", params.EndDate)
	if err != nil {
		response.ReturnError(c, response.INVALID_ARGUMENT, "结束日期格式错误")
		return
	}

	calendars, err := system.GetPublishCalendarByDateRange(startDate, endDate)
	if err != nil {
		response.ReturnError(c, response.DATA_LOSS, "查询日历失败")
		return
	}

	// 获取对应日期的投稿数据
	submissions, err := system.GetSubmissionsByDateRange(startDate, endDate, "")
	if err != nil {
		response.ReturnError(c, response.DATA_LOSS, "查询投稿失败")
		return
	}

	// 组织返回数据
	result := map[string]interface{}{
		"calendars":   calendars,
		"submissions": submissions,
	}

	response.ReturnData(c, result)
}

// UpdatePublishCalendar 更新发布日历
func UpdatePublishCalendar(c *gin.Context) {
	params := &struct {
		Date           string `json:"date" form:"date" binding:"required"`
		MaxSubmissions *uint  `json:"max_submissions" form:"max_submissions"`
		Notes          string `json:"notes" form:"notes"`
		IsHoliday      *bool  `json:"is_holiday" form:"is_holiday"`
	}{}
	if !middleware.CheckParam(params, c) {
		return
	}

	// 检查管理员权限
	jwtData, exists := c.Get(middleware.JWTDataKey)
	if !exists {
		response.ReturnError(c, response.UNAUTHENTICATED, "用户未登录")
		return
	}
	userData := jwtData.(*authentication.JWTData)
	if userData.RoleID != 1 {
		response.ReturnError(c, response.PERMISSION_DENIED, "无权限执行此操作")
		return
	}

	// 解析日期
	date, err := time.Parse("2006-01-02", params.Date)
	if err != nil {
		response.ReturnError(c, response.INVALID_ARGUMENT, "日期格式错误")
		return
	}

	// 获取或创建日历记录
	calendar, err := system.GetOrCreatePublishCalendar(date)
	if err != nil {
		response.ReturnError(c, response.DATA_LOSS, "获取日历失败")
		return
	}

	// 更新日历信息
	if params.MaxSubmissions != nil {
		calendar.MaxSubmissions = *params.MaxSubmissions
	}
	if params.Notes != "" {
		calendar.Notes = params.Notes
	}
	if params.IsHoliday != nil {
		calendar.IsHoliday = *params.IsHoliday
	}

	err = system.UpdatePublishCalendar(calendar)
	if err != nil {
		response.ReturnError(c, response.DATA_LOSS, "更新日历失败")
		return
	}

	response.ReturnData(c, calendar)
}

// GetAvailablePublishDates 获取可用发布日期
func GetAvailablePublishDates(c *gin.Context) {
	params := &struct {
		StartDate string `json:"start_date" form:"start_date" binding:"required"`
		EndDate   string `json:"end_date" form:"end_date" binding:"required"`
	}{}
	if !middleware.CheckParam(params, c) {
		return
	}

	// 解析日期
	startDate, err := time.Parse("2006-01-02", params.StartDate)
	if err != nil {
		response.ReturnError(c, response.INVALID_ARGUMENT, "开始日期格式错误")
		return
	}

	endDate, err := time.Parse("2006-01-02", params.EndDate)
	if err != nil {
		response.ReturnError(c, response.INVALID_ARGUMENT, "结束日期格式错误")
		return
	}

	availableDates, err := system.GetAvailablePublishDates(startDate, endDate)
	if err != nil {
		response.ReturnError(c, response.DATA_LOSS, "查询可用日期失败")
		return
	}

	// 格式化日期为字符串
	var dateStrings []string
	for _, date := range availableDates {
		dateStrings = append(dateStrings, date.Format("2006-01-02"))
	}

	response.ReturnData(c, dateStrings)
}

// GetCalendarStatistics 获取日历统计
func GetCalendarStatistics(c *gin.Context) {
	params := &struct {
		StartDate string `json:"start_date" form:"start_date" binding:"required"`
		EndDate   string `json:"end_date" form:"end_date" binding:"required"`
	}{}
	if !middleware.CheckParam(params, c) {
		return
	}

	// 解析日期
	startDate, err := time.Parse("2006-01-02", params.StartDate)
	if err != nil {
		response.ReturnError(c, response.INVALID_ARGUMENT, "开始日期格式错误")
		return
	}

	endDate, err := time.Parse("2006-01-02", params.EndDate)
	if err != nil {
		response.ReturnError(c, response.INVALID_ARGUMENT, "结束日期格式错误")
		return
	}

	stats, err := system.GetCalendarStatistics(startDate, endDate)
	if err != nil {
		response.ReturnError(c, response.DATA_LOSS, "获取统计信息失败")
		return
	}

	response.ReturnData(c, stats)
}

// SetHolidayDates 设置节假日
func SetHolidayDates(c *gin.Context) {
	params := &struct {
		Dates     []string `json:"dates" form:"dates" binding:"required"`
		IsHoliday bool     `json:"is_holiday" form:"is_holiday"`
	}{}
	if !middleware.CheckParam(params, c) {
		return
	}

	// 检查管理员权限
	jwtData, exists := c.Get(middleware.JWTDataKey)
	if !exists {
		response.ReturnError(c, response.UNAUTHENTICATED, "用户未登录")
		return
	}
	userData := jwtData.(*authentication.JWTData)
	if userData.RoleID != 1 {
		response.ReturnError(c, response.PERMISSION_DENIED, "无权限执行此操作")
		return
	}

	// 解析日期
	var dates []time.Time
	for _, dateStr := range params.Dates {
		date, err := time.Parse("2006-01-02", dateStr)
		if err != nil {
			response.ReturnError(c, response.INVALID_ARGUMENT, "日期格式错误: "+dateStr)
			return
		}
		dates = append(dates, date)
	}

	err := system.SetHolidayDates(dates, params.IsHoliday)
	if err != nil {
		response.ReturnError(c, response.DATA_LOSS, "设置节假日失败")
		return
	}

	response.ReturnSuccess(c)
}

// GetSubmissionsByDate 根据日期获取投稿
func GetSubmissionsByDate(c *gin.Context) {
	params := &struct {
		Date   string `json:"date" form:"date" binding:"required"`
		Status string `json:"status" form:"status"`
	}{}
	if !middleware.CheckParam(params, c) {
		return
	}

	// 解析日期
	date, err := time.Parse("2006-01-02", params.Date)
	if err != nil {
		response.ReturnError(c, response.INVALID_ARGUMENT, "日期格式错误")
		return
	}

	// 查询当天的投稿
	endDate := date.AddDate(0, 0, 1) // 第二天
	submissions, err := system.GetSubmissionsByDateRange(date, endDate, system.SubmissionStatus(params.Status))
	if err != nil {
		response.ReturnError(c, response.DATA_LOSS, "查询投稿失败")
		return
	}

	response.ReturnData(c, submissions)
}
