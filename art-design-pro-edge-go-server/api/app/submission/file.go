package submission

import (
	"fmt"
	"io"
	"mime/multipart"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"api-server/api/middleware"
	"api-server/api/response"
	"api-server/db/pgdb/system"
	"api-server/util/authentication"
	"api-server/util/id"
)

// UploadFile 上传文件
func UploadFile(c *gin.Context) {
	// 获取投稿ID
	submissionIDStr := c.PostForm("submission_id")
	submissionID, err := strconv.ParseUint(submissionIDStr, 10, 32)
	if err != nil {
		response.ReturnError(c, response.INVALID_ARGUMENT, "投稿ID无效")
		return
	}

	// 获取当前用户信息
	jwtData, exists := c.Get(middleware.JWTDataKey)
	if !exists {
		response.ReturnError(c, response.UNAUTHENTICATED, "用户未登录")
		return
	}
	userData := jwtData.(*authentication.JWTData)

	// 检查投稿是否存在且有权限
	submission := system.Submission{Model: gorm.Model{ID: uint(submissionID)}}
	err = system.GetSubmission(&submission)
	if err != nil {
		response.ReturnError(c, response.DATA_LOSS, "投稿不存在")
		return
	}

	// 检查权限
	if submission.UserID != userData.UserID && userData.RoleID != 1 {
		response.ReturnError(c, response.PERMISSION_DENIED, "无权限上传文件到此投稿")
		return
	}

	// 检查投稿状态
	if submission.Status != system.SubmissionStatusDraft && submission.Status != system.SubmissionStatusRevision {
		response.ReturnError(c, response.INVALID_ARGUMENT, "当前状态不允许上传文件")
		return
	}

	// 获取上传的文件
	file, header, err := c.Request.FormFile("file")
	if err != nil {
		response.ReturnError(c, response.INVALID_ARGUMENT, "获取文件失败")
		return
	}
	defer file.Close()

	// 验证文件
	if err := validateFile(header); err != nil {
		response.ReturnError(c, response.INVALID_ARGUMENT, err.Error())
		return
	}

	// 生成文件存储信息
	fileExt := filepath.Ext(header.Filename)
	storedName := id.GenerateID() + fileExt
	uploadDir := getUploadDir()
	filePath := filepath.Join(uploadDir, storedName)

	// 确保上传目录存在
	if err := os.MkdirAll(uploadDir, 0755); err != nil {
		response.ReturnError(c, response.INTERNAL_ERROR, "创建上传目录失败")
		return
	}

	// 保存文件
	dst, err := os.Create(filePath)
	if err != nil {
		response.ReturnError(c, response.INTERNAL_ERROR, "创建文件失败")
		return
	}
	defer dst.Close()

	if _, err := io.Copy(dst, file); err != nil {
		response.ReturnError(c, response.INTERNAL_ERROR, "保存文件失败")
		return
	}

	// 获取文件信息
	fileInfo, err := dst.Stat()
	if err != nil {
		response.ReturnError(c, response.INTERNAL_ERROR, "获取文件信息失败")
		return
	}

	// 保存文件记录到数据库
	submissionFile := system.SubmissionFile{
		SubmissionID: uint(submissionID),
		OriginalName: header.Filename,
		StoredName:   storedName,
		FilePath:     filePath,
		FileType:     fileExt,
		FileSize:     fileInfo.Size(),
		MimeType:     header.Header.Get("Content-Type"),
		FileCategory: getFileCategory(header.Filename),
		Description:  c.PostForm("description"),
	}

	err = system.AddSubmissionFile(&submissionFile)
	if err != nil {
		// 如果数据库保存失败，删除已上传的文件
		os.Remove(filePath)
		response.ReturnError(c, response.DATA_LOSS, "保存文件记录失败")
		return
	}

	response.ReturnData(c, submissionFile)
}

// GetFileList 获取文件列表
func GetFileList(c *gin.Context) {
	params := &struct {
		SubmissionID uint   `json:"submission_id" form:"submission_id"`
		FileType     string `json:"file_type" form:"file_type"`
		FileCategory string `json:"file_category" form:"file_category"`
	}{}
	if !middleware.CheckParam(params, c) {
		return
	}

	// 获取分页参数
	page := middleware.GetPage(c)
	pageSize := middleware.GetPageSize(c)

	file := system.SubmissionFile{
		SubmissionID: params.SubmissionID,
		FileType:     params.FileType,
		FileCategory: params.FileCategory,
	}

	files, total, err := system.FindSubmissionFileList(&file, page, pageSize)
	if err != nil {
		response.ReturnError(c, response.DATA_LOSS, "查询文件失败")
		return
	}

	response.ReturnDataWithCount(c, int(total), files)
}

// DeleteFile 删除文件
func DeleteFile(c *gin.Context) {
	params := &struct {
		ID uint `json:"id" form:"id" binding:"required"`
	}{}
	if !middleware.CheckParam(params, c) {
		return
	}

	// 获取当前用户信息
	jwtData, exists := c.Get(middleware.JWTDataKey)
	if !exists {
		response.ReturnError(c, response.UNAUTHENTICATED, "用户未登录")
		return
	}
	userData := jwtData.(*authentication.JWTData)

	// 获取文件信息
	file := system.SubmissionFile{Model: gorm.Model{ID: params.ID}}
	err := system.GetSubmissionFile(&file)
	if err != nil {
		response.ReturnError(c, response.DATA_LOSS, "文件不存在")
		return
	}

	// 检查权限
	submission := system.Submission{Model: gorm.Model{ID: file.SubmissionID}}
	err = system.GetSubmission(&submission)
	if err != nil {
		response.ReturnError(c, response.DATA_LOSS, "投稿不存在")
		return
	}

	if submission.UserID != userData.UserID && userData.RoleID != 1 {
		response.ReturnError(c, response.PERMISSION_DENIED, "无权限删除此文件")
		return
	}

	// 删除文件记录
	err = system.DeleteSubmissionFile(&file)
	if err != nil {
		response.ReturnError(c, response.DATA_LOSS, "删除文件记录失败")
		return
	}

	// 删除物理文件
	if err := os.Remove(file.FilePath); err != nil {
		// 记录日志但不返回错误，因为数据库记录已删除
		fmt.Printf("删除物理文件失败: %v\n", err)
	}

	response.ReturnData(c, file)
}

// DownloadFile 下载文件
func DownloadFile(c *gin.Context) {
	params := &struct {
		ID uint `json:"id" form:"id" binding:"required"`
	}{}
	if !middleware.CheckParam(params, c) {
		return
	}

	// 获取文件信息
	file := system.SubmissionFile{Model: gorm.Model{ID: params.ID}}
	err := system.GetSubmissionFile(&file)
	if err != nil {
		response.ReturnError(c, response.DATA_LOSS, "文件不存在")
		return
	}

	// 检查文件是否存在
	if _, err := os.Stat(file.FilePath); os.IsNotExist(err) {
		response.ReturnError(c, response.DATA_LOSS, "文件不存在")
		return
	}

	// 设置响应头
	c.Header("Content-Description", "File Transfer")
	c.Header("Content-Transfer-Encoding", "binary")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", file.OriginalName))
	c.Header("Content-Type", "application/octet-stream")

	// 发送文件
	c.File(file.FilePath)
}

// UpdateFileSortOrder 更新文件排序
func UpdateFileSortOrder(c *gin.Context) {
	params := &struct {
		FileOrders []struct {
			ID        uint `json:"id"`
			SortOrder uint `json:"sort_order"`
		} `json:"file_orders" binding:"required"`
	}{}
	if !middleware.CheckParam(params, c) {
		return
	}

	err := system.UpdateFilesSortOrder(params.FileOrders)
	if err != nil {
		response.ReturnError(c, response.DATA_LOSS, "更新排序失败")
		return
	}

	response.ReturnSuccess(c)
}

// GetFileStatistics 获取文件统计
func GetFileStatistics(c *gin.Context) {
	stats, err := system.GetFileStatistics()
	if err != nil {
		response.ReturnError(c, response.DATA_LOSS, "获取统计信息失败")
		return
	}

	response.ReturnData(c, stats)
}

// 验证文件
func validateFile(header *multipart.FileHeader) error {
	// 检查文件大小（50MB限制）
	const maxFileSize = 50 * 1024 * 1024
	if header.Size > maxFileSize {
		return fmt.Errorf("文件大小不能超过50MB")
	}

	// 检查文件扩展名
	ext := strings.ToLower(filepath.Ext(header.Filename))
	allowedExts := map[string]bool{
		".jpg": true, ".jpeg": true, ".png": true, ".gif": true, ".bmp": true,
		".mp4": true, ".avi": true, ".mov": true, ".wmv": true, ".flv": true,
		".pdf": true, ".doc": true, ".docx": true, ".xls": true, ".xlsx": true,
		".ppt": true, ".pptx": true, ".txt": true, ".zip": true, ".rar": true,
	}

	if !allowedExts[ext] {
		return fmt.Errorf("不支持的文件类型: %s", ext)
	}

	return nil
}

// 获取文件分类
func getFileCategory(filename string) string {
	ext := strings.ToLower(filepath.Ext(filename))
	
	imageExts := map[string]bool{
		".jpg": true, ".jpeg": true, ".png": true, ".gif": true, ".bmp": true,
	}
	videoExts := map[string]bool{
		".mp4": true, ".avi": true, ".mov": true, ".wmv": true, ".flv": true,
	}
	
	if imageExts[ext] {
		return "image"
	} else if videoExts[ext] {
		return "video"
	} else {
		return "document"
	}
}

// 获取上传目录
func getUploadDir() string {
	now := time.Now()
	return filepath.Join("uploads", "submissions", now.Format("2006"), now.Format("01"), now.Format("02"))
}
