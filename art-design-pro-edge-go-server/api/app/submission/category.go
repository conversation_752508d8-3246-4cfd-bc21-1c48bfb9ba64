package submission

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"api-server/api/middleware"
	"api-server/api/response"
	"api-server/db/pgdb/system"
	"api-server/util/authentication"
)

// GetCategoryList 获取分类列表
func GetCategoryList(c *gin.Context) {
	params := &struct {
		Name     string `json:"name" form:"name"`
		IsActive *bool  `json:"is_active" form:"is_active"`
	}{}
	if !middleware.CheckParam(params, c) {
		return
	}

	// 获取分页参数
	page := middleware.GetPage(c)
	pageSize := middleware.GetPageSize(c)

	category := system.Category{
		Name: params.Name,
	}
	if params.IsActive != nil {
		category.IsActive = *params.IsActive
	}

	categories, total, err := system.FindCategoryList(&category, page, pageSize)
	if err != nil {
		response.ReturnError(c, response.DATA_LOSS, "查询分类失败")
		return
	}

	response.ReturnDataWithCount(c, int(total), categories)
}

// GetAllActiveCategories 获取所有启用的分类
func GetAllActiveCategories(c *gin.Context) {
	categories, err := system.GetAllActiveCategories()
	if err != nil {
		response.ReturnError(c, response.DATA_LOSS, "查询分类失败")
		return
	}

	response.ReturnData(c, categories)
}

// CreateCategory 创建分类
func CreateCategory(c *gin.Context) {
	params := &struct {
		Name        string `json:"name" form:"name" binding:"required"`
		Description string `json:"description" form:"description"`
		SortOrder   uint   `json:"sort_order" form:"sort_order"`
	}{}
	if !middleware.CheckParam(params, c) {
		return
	}

	// 检查管理员权限
	jwtData, exists := c.Get(middleware.JWTDataKey)
	if !exists {
		response.ReturnError(c, response.UNAUTHENTICATED, "用户未登录")
		return
	}
	userData := jwtData.(*authentication.JWTData)
	if userData.RoleID != 1 {
		response.ReturnError(c, response.PERMISSION_DENIED, "无权限执行此操作")
		return
	}

	category := system.Category{
		Name:        params.Name,
		Description: params.Description,
		SortOrder:   params.SortOrder,
		IsActive:    true,
	}

	err := system.AddCategory(&category)
	if err != nil {
		response.ReturnError(c, response.DATA_LOSS, "创建分类失败")
		return
	}

	response.ReturnData(c, category)
}

// UpdateCategory 更新分类
func UpdateCategory(c *gin.Context) {
	params := &struct {
		ID          uint   `json:"id" form:"id" binding:"required"`
		Name        string `json:"name" form:"name" binding:"required"`
		Description string `json:"description" form:"description"`
		SortOrder   uint   `json:"sort_order" form:"sort_order"`
		IsActive    *bool  `json:"is_active" form:"is_active"`
	}{}
	if !middleware.CheckParam(params, c) {
		return
	}

	// 检查管理员权限
	jwtData, exists := c.Get(middleware.JWTDataKey)
	if !exists {
		response.ReturnError(c, response.UNAUTHENTICATED, "用户未登录")
		return
	}
	userData := jwtData.(*authentication.JWTData)
	if userData.RoleID != 1 {
		response.ReturnError(c, response.PERMISSION_DENIED, "无权限执行此操作")
		return
	}

	// 检查分类是否存在
	category := system.Category{Model: gorm.Model{ID: params.ID}}
	err := system.GetCategory(&category)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			response.ReturnError(c, response.DATA_LOSS, "分类不存在")
			return
		}
		response.ReturnError(c, response.DATA_LOSS, "查询分类失败")
		return
	}

	// 更新分类信息
	category.Name = params.Name
	category.Description = params.Description
	category.SortOrder = params.SortOrder
	if params.IsActive != nil {
		category.IsActive = *params.IsActive
	}

	err = system.UpdateCategory(&category)
	if err != nil {
		response.ReturnError(c, response.DATA_LOSS, "更新分类失败")
		return
	}

	response.ReturnData(c, category)
}

// DeleteCategory 删除分类
func DeleteCategory(c *gin.Context) {
	params := &struct {
		ID uint `json:"id" form:"id" binding:"required"`
	}{}
	if !middleware.CheckParam(params, c) {
		return
	}

	// 检查管理员权限
	jwtData, exists := c.Get(middleware.JWTDataKey)
	if !exists {
		response.ReturnError(c, response.UNAUTHENTICATED, "用户未登录")
		return
	}
	userData := jwtData.(*authentication.JWTData)
	if userData.RoleID != 1 {
		response.ReturnError(c, response.PERMISSION_DENIED, "无权限执行此操作")
		return
	}

	// 检查分类是否存在
	category := system.Category{Model: gorm.Model{ID: params.ID}}
	err := system.GetCategory(&category)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			response.ReturnError(c, response.DATA_LOSS, "分类不存在")
			return
		}
		response.ReturnError(c, response.DATA_LOSS, "查询分类失败")
		return
	}

	err = system.DeleteCategory(&category)
	if err != nil {
		response.ReturnError(c, response.DATA_LOSS, "删除分类失败")
		return
	}

	response.ReturnData(c, category)
}

// UpdateCategorySortOrder 更新分类排序
func UpdateCategorySortOrder(c *gin.Context) {
	params := &struct {
		CategoryOrders []struct {
			ID        uint `json:"id"`
			SortOrder uint `json:"sort_order"`
		} `json:"category_orders" binding:"required"`
	}{}
	if !middleware.CheckParam(params, c) {
		return
	}

	// 检查管理员权限
	jwtData, exists := c.Get(middleware.JWTDataKey)
	if !exists {
		response.ReturnError(c, response.UNAUTHENTICATED, "用户未登录")
		return
	}
	userData := jwtData.(*authentication.JWTData)
	if userData.RoleID != 1 {
		response.ReturnError(c, response.PERMISSION_DENIED, "无权限执行此操作")
		return
	}

	err := system.UpdateCategorySortOrder(params.CategoryOrders)
	if err != nil {
		response.ReturnError(c, response.DATA_LOSS, "更新排序失败")
		return
	}

	response.ReturnSuccess(c)
}
