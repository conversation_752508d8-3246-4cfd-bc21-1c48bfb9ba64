package submission

import (
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"api-server/api/middleware"
	"api-server/api/response"
	"api-server/db/pgdb/system"
	"api-server/util/authentication"
)

// GetSubmissionList 获取投稿列表
func GetSubmissionList(c *gin.Context) {
	params := &struct {
		Title  string `json:"title" form:"title"`
		Status string `json:"status" form:"status"`
		Type   string `json:"type" form:"type"`
		UserID uint   `json:"user_id" form:"user_id"`
	}{}
	if !middleware.CheckParam(params, c) {
		return
	}

	// 获取分页参数
	page := middleware.GetPage(c)
	pageSize := middleware.GetPageSize(c)

	submission := system.Submission{
		Title:  params.Title,
		Status: system.SubmissionStatus(params.Status),
		Type:   system.SubmissionType(params.Type),
		UserID: params.UserID,
	}

	// 调用带分页的查询函数
	submissions, total, err := system.FindSubmissionList(&submission, page, pageSize)
	if err != nil {
		response.ReturnError(c, response.DATA_LOSS, "查询投稿失败")
		return
	}

	// 返回带总数的结果
	response.ReturnDataWithCount(c, int(total), submissions)
}

// GetSubmissionDetail 获取投稿详情
func GetSubmissionDetail(c *gin.Context) {
	params := &struct {
		ID uint `json:"id" form:"id" binding:"required"`
	}{}
	if !middleware.CheckParam(params, c) {
		return
	}

	submission := system.Submission{Model: gorm.Model{ID: params.ID}}
	err := system.GetSubmission(&submission)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			response.ReturnError(c, response.DATA_LOSS, "投稿不存在")
			return
		}
		response.ReturnError(c, response.DATA_LOSS, "查询投稿失败")
		return
	}

	response.ReturnData(c, submission)
}

// CreateSubmission 创建投稿
func CreateSubmission(c *gin.Context) {
	params := &struct {
		Title       string   `json:"title" form:"title" binding:"required"`
		Description string   `json:"description" form:"description"`
		Type        string   `json:"type" form:"type" binding:"required"`
		Tags        string   `json:"tags" form:"tags"`
		Keywords    string   `json:"keywords" form:"keywords"`
		CategoryIDs []uint   `json:"category_ids" form:"category_ids"`
	}{}
	if !middleware.CheckParam(params, c) {
		return
	}

	// 获取当前用户信息
	jwtData, exists := c.Get(middleware.JWTDataKey)
	if !exists {
		response.ReturnError(c, response.UNAUTHENTICATED, "用户未登录")
		return
	}
	userData := jwtData.(*authentication.JWTData)

	submission := system.Submission{
		UserID:      userData.UserID,
		Title:       params.Title,
		Description: params.Description,
		Type:        system.SubmissionType(params.Type),
		Status:      system.SubmissionStatusDraft,
		Priority:    system.SubmissionPriorityNormal,
		Tags:        params.Tags,
		Keywords:    params.Keywords,
	}

	err := system.AddSubmission(&submission)
	if err != nil {
		response.ReturnError(c, response.DATA_LOSS, "创建投稿失败")
		return
	}

	response.ReturnData(c, submission)
}

// UpdateSubmission 更新投稿
func UpdateSubmission(c *gin.Context) {
	params := &struct {
		ID          uint     `json:"id" form:"id" binding:"required"`
		Title       string   `json:"title" form:"title" binding:"required"`
		Description string   `json:"description" form:"description"`
		Type        string   `json:"type" form:"type" binding:"required"`
		Tags        string   `json:"tags" form:"tags"`
		Keywords    string   `json:"keywords" form:"keywords"`
		CategoryIDs []uint   `json:"category_ids" form:"category_ids"`
	}{}
	if !middleware.CheckParam(params, c) {
		return
	}

	// 获取当前用户信息
	jwtData, exists := c.Get(middleware.JWTDataKey)
	if !exists {
		response.ReturnError(c, response.UNAUTHENTICATED, "用户未登录")
		return
	}
	userData := jwtData.(*authentication.JWTData)

	// 检查投稿是否存在
	submission := system.Submission{Model: gorm.Model{ID: params.ID}}
	err := system.GetSubmission(&submission)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			response.ReturnError(c, response.DATA_LOSS, "投稿不存在")
			return
		}
		response.ReturnError(c, response.DATA_LOSS, "查询投稿失败")
		return
	}

	// 检查权限：只有投稿者本人或管理员可以修改
	if submission.UserID != userData.UserID && userData.RoleID != 1 {
		response.ReturnError(c, response.PERMISSION_DENIED, "无权限修改此投稿")
		return
	}

	// 检查状态：只有草稿和需修改状态的投稿可以编辑
	if submission.Status != system.SubmissionStatusDraft && submission.Status != system.SubmissionStatusRevision {
		response.ReturnError(c, response.INVALID_ARGUMENT, "当前状态不允许修改")
		return
	}

	// 更新投稿信息
	submission.Title = params.Title
	submission.Description = params.Description
	submission.Type = system.SubmissionType(params.Type)
	submission.Tags = params.Tags
	submission.Keywords = params.Keywords

	err = system.UpdateSubmission(&submission)
	if err != nil {
		response.ReturnError(c, response.DATA_LOSS, "更新投稿失败")
		return
	}

	response.ReturnData(c, submission)
}

// DeleteSubmission 删除投稿
func DeleteSubmission(c *gin.Context) {
	params := &struct {
		ID uint `json:"id" form:"id" binding:"required"`
	}{}
	if !middleware.CheckParam(params, c) {
		return
	}

	// 获取当前用户信息
	jwtData, exists := c.Get(middleware.JWTDataKey)
	if !exists {
		response.ReturnError(c, response.UNAUTHENTICATED, "用户未登录")
		return
	}
	userData := jwtData.(*authentication.JWTData)

	// 检查投稿是否存在
	submission := system.Submission{Model: gorm.Model{ID: params.ID}}
	err := system.GetSubmission(&submission)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			response.ReturnError(c, response.DATA_LOSS, "投稿不存在")
			return
		}
		response.ReturnError(c, response.DATA_LOSS, "查询投稿失败")
		return
	}

	// 检查权限：只有投稿者本人或管理员可以删除
	if submission.UserID != userData.UserID && userData.RoleID != 1 {
		response.ReturnError(c, response.PERMISSION_DENIED, "无权限删除此投稿")
		return
	}

	// 检查状态：只有草稿状态的投稿可以删除
	if submission.Status != system.SubmissionStatusDraft {
		response.ReturnError(c, response.INVALID_ARGUMENT, "只能删除草稿状态的投稿")
		return
	}

	err = system.DeleteSubmission(&submission)
	if err != nil {
		response.ReturnError(c, response.DATA_LOSS, "删除投稿失败")
		return
	}

	response.ReturnData(c, submission)
}

// SubmitSubmission 提交投稿
func SubmitSubmission(c *gin.Context) {
	params := &struct {
		ID uint `json:"id" form:"id" binding:"required"`
	}{}
	if !middleware.CheckParam(params, c) {
		return
	}

	// 获取当前用户信息
	jwtData, exists := c.Get(middleware.JWTDataKey)
	if !exists {
		response.ReturnError(c, response.UNAUTHENTICATED, "用户未登录")
		return
	}
	userData := jwtData.(*authentication.JWTData)

	// 检查投稿是否存在
	submission := system.Submission{Model: gorm.Model{ID: params.ID}}
	err := system.GetSubmission(&submission)
	if err != nil {
		response.ReturnError(c, response.DATA_LOSS, "投稿不存在")
		return
	}

	// 检查权限
	if submission.UserID != userData.UserID {
		response.ReturnError(c, response.PERMISSION_DENIED, "无权限操作此投稿")
		return
	}

	// 检查状态
	if submission.Status != system.SubmissionStatusDraft && submission.Status != system.SubmissionStatusRevision {
		response.ReturnError(c, response.INVALID_ARGUMENT, "当前状态不允许提交")
		return
	}

	// 更新状态
	err = system.UpdateSubmissionStatus(params.ID, submission.Status, system.SubmissionStatusSubmitted, userData.UserID, "用户提交投稿")
	if err != nil {
		response.ReturnError(c, response.DATA_LOSS, "提交投稿失败")
		return
	}

	response.ReturnSuccess(c)
}

// GetMySubmissions 获取我的投稿列表
func GetMySubmissions(c *gin.Context) {
	// 获取当前用户信息
	jwtData, exists := c.Get(middleware.JWTDataKey)
	if !exists {
		response.ReturnError(c, response.UNAUTHENTICATED, "用户未登录")
		return
	}
	userData := jwtData.(*authentication.JWTData)

	params := &struct {
		Status string `json:"status" form:"status"`
		Type   string `json:"type" form:"type"`
	}{}
	if !middleware.CheckParam(params, c) {
		return
	}

	// 获取分页参数
	page := middleware.GetPage(c)
	pageSize := middleware.GetPageSize(c)

	submission := system.Submission{
		UserID: userData.UserID,
		Status: system.SubmissionStatus(params.Status),
		Type:   system.SubmissionType(params.Type),
	}

	// 调用带分页的查询函数
	submissions, total, err := system.FindSubmissionList(&submission, page, pageSize)
	if err != nil {
		response.ReturnError(c, response.DATA_LOSS, "查询投稿失败")
		return
	}

	// 返回带总数的结果
	response.ReturnDataWithCount(c, int(total), submissions)
}

// GetSubmissionStatistics 获取投稿统计
func GetSubmissionStatistics(c *gin.Context) {
	userIDStr := c.Query("user_id")
	var userID uint = 0
	
	if userIDStr != "" {
		if id, err := strconv.ParseUint(userIDStr, 10, 32); err == nil {
			userID = uint(id)
		}
	}

	// 如果没有指定用户ID，则获取当前用户的统计
	if userID == 0 {
		jwtData, exists := c.Get(middleware.JWTDataKey)
		if !exists {
			response.ReturnError(c, response.UNAUTHENTICATED, "用户未登录")
			return
		}
		userData := jwtData.(*authentication.JWTData)
		userID = userData.UserID
	}

	stats, err := system.GetSubmissionStatistics(userID)
	if err != nil {
		response.ReturnError(c, response.DATA_LOSS, "获取统计信息失败")
		return
	}

	response.ReturnData(c, stats)
}
