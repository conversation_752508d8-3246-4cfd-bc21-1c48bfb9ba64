package submission

import (
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"api-server/api/middleware"
	"api-server/api/response"
	"api-server/db/pgdb/system"
	"api-server/util/authentication"
)

// UpdateSubmissionStatus 更新投稿状态（管理员操作）
func UpdateSubmissionStatus(c *gin.Context) {
	params := &struct {
		ID        uint   `json:"id" form:"id" binding:"required"`
		Status    string `json:"status" form:"status" binding:"required"`
		Comment   string `json:"comment" form:"comment"`
		AdminNote string `json:"admin_note" form:"admin_note"`
	}{}
	if !middleware.CheckParam(params, c) {
		return
	}

	// 获取当前用户信息
	jwtData, exists := c.Get(middleware.JWTDataKey)
	if !exists {
		response.ReturnError(c, response.UNAUTHENTICATED, "用户未登录")
		return
	}
	userData := jwtData.(*authentication.JWTData)

	// 检查管理员权限
	if userData.RoleID != 1 {
		response.ReturnError(c, response.PERMISSION_DENIED, "无权限执行此操作")
		return
	}

	// 检查投稿是否存在
	submission := system.Submission{Model: gorm.Model{ID: params.ID}}
	err := system.GetSubmission(&submission)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			response.ReturnError(c, response.DATA_LOSS, "投稿不存在")
			return
		}
		response.ReturnError(c, response.DATA_LOSS, "查询投稿失败")
		return
	}

	oldStatus := submission.Status
	newStatus := system.SubmissionStatus(params.Status)

	// 验证状态转换的合法性
	if !isValidStatusTransition(oldStatus, newStatus) {
		response.ReturnError(c, response.INVALID_ARGUMENT, "无效的状态转换")
		return
	}

	// 更新管理员备注
	if params.AdminNote != "" {
		submission.AdminNotes = params.AdminNote
		err = system.UpdateSubmission(&submission)
		if err != nil {
			response.ReturnError(c, response.DATA_LOSS, "更新投稿失败")
			return
		}
	}

	// 更新状态
	err = system.UpdateSubmissionStatus(params.ID, oldStatus, newStatus, userData.UserID, params.Comment)
	if err != nil {
		response.ReturnError(c, response.DATA_LOSS, "更新状态失败")
		return
	}

	response.ReturnSuccess(c)
}

// ScheduleSubmission 安排投稿发布
func ScheduleSubmission(c *gin.Context) {
	params := &struct {
		ID          uint   `json:"id" form:"id" binding:"required"`
		PublishDate string `json:"publish_date" form:"publish_date" binding:"required"`
		Comment     string `json:"comment" form:"comment"`
	}{}
	if !middleware.CheckParam(params, c) {
		return
	}

	// 获取当前用户信息
	jwtData, exists := c.Get(middleware.JWTDataKey)
	if !exists {
		response.ReturnError(c, response.UNAUTHENTICATED, "用户未登录")
		return
	}
	userData := jwtData.(*authentication.JWTData)

	// 检查管理员权限
	if userData.RoleID != 1 {
		response.ReturnError(c, response.PERMISSION_DENIED, "无权限执行此操作")
		return
	}

	// 解析发布日期
	publishDate, err := time.Parse("2006-01-02", params.PublishDate)
	if err != nil {
		response.ReturnError(c, response.INVALID_ARGUMENT, "发布日期格式错误")
		return
	}

	// 检查投稿是否存在
	submission := system.Submission{Model: gorm.Model{ID: params.ID}}
	err = system.GetSubmission(&submission)
	if err != nil {
		response.ReturnError(c, response.DATA_LOSS, "投稿不存在")
		return
	}

	// 检查状态
	if submission.Status != system.SubmissionStatusApproved {
		response.ReturnError(c, response.INVALID_ARGUMENT, "只有已通过的投稿可以安排发布")
		return
	}

	// 检查发布日期是否可用
	calendar, err := system.GetOrCreatePublishCalendar(publishDate)
	if err != nil {
		response.ReturnError(c, response.DATA_LOSS, "获取发布日历失败")
		return
	}

	if calendar.IsHoliday {
		response.ReturnError(c, response.INVALID_ARGUMENT, "不能在节假日发布")
		return
	}

	if calendar.CurrentSubmissions >= calendar.MaxSubmissions {
		response.ReturnError(c, response.INVALID_ARGUMENT, "该日期发布数量已满")
		return
	}

	// 更新投稿发布日期和状态
	submission.PublishDate = &publishDate
	err = system.UpdateSubmission(&submission)
	if err != nil {
		response.ReturnError(c, response.DATA_LOSS, "更新投稿失败")
		return
	}

	// 更新状态为待发布
	err = system.UpdateSubmissionStatus(params.ID, submission.Status, system.SubmissionStatusScheduled, userData.UserID, params.Comment)
	if err != nil {
		response.ReturnError(c, response.DATA_LOSS, "更新状态失败")
		return
	}

	// 更新日历计数
	err = system.UpdateCalendarSubmissionCount(publishDate, true)
	if err != nil {
		response.ReturnError(c, response.DATA_LOSS, "更新日历失败")
		return
	}

	response.ReturnSuccess(c)
}

// PublishSubmission 发布投稿
func PublishSubmission(c *gin.Context) {
	params := &struct {
		ID      uint   `json:"id" form:"id" binding:"required"`
		Comment string `json:"comment" form:"comment"`
	}{}
	if !middleware.CheckParam(params, c) {
		return
	}

	// 获取当前用户信息
	jwtData, exists := c.Get(middleware.JWTDataKey)
	if !exists {
		response.ReturnError(c, response.UNAUTHENTICATED, "用户未登录")
		return
	}
	userData := jwtData.(*authentication.JWTData)

	// 检查管理员权限
	if userData.RoleID != 1 {
		response.ReturnError(c, response.PERMISSION_DENIED, "无权限执行此操作")
		return
	}

	// 检查投稿是否存在
	submission := system.Submission{Model: gorm.Model{ID: params.ID}}
	err := system.GetSubmission(&submission)
	if err != nil {
		response.ReturnError(c, response.DATA_LOSS, "投稿不存在")
		return
	}

	// 检查状态
	if submission.Status != system.SubmissionStatusScheduled {
		response.ReturnError(c, response.INVALID_ARGUMENT, "只有待发布的投稿可以发布")
		return
	}

	// 更新状态为已发布
	err = system.UpdateSubmissionStatus(params.ID, submission.Status, system.SubmissionStatusPublished, userData.UserID, params.Comment)
	if err != nil {
		response.ReturnError(c, response.DATA_LOSS, "发布失败")
		return
	}

	response.ReturnSuccess(c)
}

// WithdrawSubmission 撤回投稿
func WithdrawSubmission(c *gin.Context) {
	params := &struct {
		ID      uint   `json:"id" form:"id" binding:"required"`
		Comment string `json:"comment" form:"comment"`
	}{}
	if !middleware.CheckParam(params, c) {
		return
	}

	// 获取当前用户信息
	jwtData, exists := c.Get(middleware.JWTDataKey)
	if !exists {
		response.ReturnError(c, response.UNAUTHENTICATED, "用户未登录")
		return
	}
	userData := jwtData.(*authentication.JWTData)

	// 检查投稿是否存在
	submission := system.Submission{Model: gorm.Model{ID: params.ID}}
	err := system.GetSubmission(&submission)
	if err != nil {
		response.ReturnError(c, response.DATA_LOSS, "投稿不存在")
		return
	}

	// 检查权限：投稿者可以撤回已提交的投稿，管理员可以撤回已发布的投稿
	canWithdraw := false
	if submission.UserID == userData.UserID && submission.Status == system.SubmissionStatusSubmitted {
		canWithdraw = true
	} else if userData.RoleID == 1 && (submission.Status == system.SubmissionStatusPublished || submission.Status == system.SubmissionStatusScheduled) {
		canWithdraw = true
	}

	if !canWithdraw {
		response.ReturnError(c, response.PERMISSION_DENIED, "无权限撤回此投稿")
		return
	}

	// 确定撤回后的状态
	var newStatus system.SubmissionStatus
	if submission.Status == system.SubmissionStatusSubmitted {
		newStatus = system.SubmissionStatusDraft
	} else {
		newStatus = system.SubmissionStatusWithdrawn
	}

	// 如果是从待发布状态撤回，需要更新日历计数
	if submission.Status == system.SubmissionStatusScheduled && submission.PublishDate != nil {
		err = system.UpdateCalendarSubmissionCount(*submission.PublishDate, false)
		if err != nil {
			response.ReturnError(c, response.DATA_LOSS, "更新日历失败")
			return
		}
	}

	// 更新状态
	err = system.UpdateSubmissionStatus(params.ID, submission.Status, newStatus, userData.UserID, params.Comment)
	if err != nil {
		response.ReturnError(c, response.DATA_LOSS, "撤回失败")
		return
	}

	response.ReturnSuccess(c)
}

// GetSubmissionLogs 获取投稿操作日志
func GetSubmissionLogs(c *gin.Context) {
	params := &struct {
		ID uint `json:"id" form:"id" binding:"required"`
	}{}
	if !middleware.CheckParam(params, c) {
		return
	}

	logs, err := system.GetSubmissionLogs(params.ID)
	if err != nil {
		response.ReturnError(c, response.DATA_LOSS, "获取日志失败")
		return
	}

	response.ReturnData(c, logs)
}

// BatchUpdateStatus 批量更新投稿状态
func BatchUpdateStatus(c *gin.Context) {
	params := &struct {
		IDs     []uint `json:"ids" form:"ids" binding:"required"`
		Status  string `json:"status" form:"status" binding:"required"`
		Comment string `json:"comment" form:"comment"`
	}{}
	if !middleware.CheckParam(params, c) {
		return
	}

	// 获取当前用户信息
	jwtData, exists := c.Get(middleware.JWTDataKey)
	if !exists {
		response.ReturnError(c, response.UNAUTHENTICATED, "用户未登录")
		return
	}
	userData := jwtData.(*authentication.JWTData)

	// 检查管理员权限
	if userData.RoleID != 1 {
		response.ReturnError(c, response.PERMISSION_DENIED, "无权限执行此操作")
		return
	}

	newStatus := system.SubmissionStatus(params.Status)
	successCount := 0
	failCount := 0

	for _, id := range params.IDs {
		submission := system.Submission{Model: gorm.Model{ID: id}}
		err := system.GetSubmission(&submission)
		if err != nil {
			failCount++
			continue
		}

		if !isValidStatusTransition(submission.Status, newStatus) {
			failCount++
			continue
		}

		err = system.UpdateSubmissionStatus(id, submission.Status, newStatus, userData.UserID, params.Comment)
		if err != nil {
			failCount++
		} else {
			successCount++
		}
	}

	result := map[string]interface{}{
		"success_count": successCount,
		"fail_count":    failCount,
		"total_count":   len(params.IDs),
	}

	response.ReturnData(c, result)
}

// 验证状态转换的合法性
func isValidStatusTransition(oldStatus, newStatus system.SubmissionStatus) bool {
	validTransitions := map[system.SubmissionStatus][]system.SubmissionStatus{
		system.SubmissionStatusDraft:     {system.SubmissionStatusSubmitted},
		system.SubmissionStatusSubmitted: {system.SubmissionStatusReviewing, system.SubmissionStatusDraft, system.SubmissionStatusRejected},
		system.SubmissionStatusReviewing: {system.SubmissionStatusApproved, system.SubmissionStatusRejected, system.SubmissionStatusRevision},
		system.SubmissionStatusRevision:  {system.SubmissionStatusDraft, system.SubmissionStatusSubmitted},
		system.SubmissionStatusApproved:  {system.SubmissionStatusScheduled, system.SubmissionStatusRejected},
		system.SubmissionStatusScheduled: {system.SubmissionStatusPublished, system.SubmissionStatusApproved, system.SubmissionStatusWithdrawn},
		system.SubmissionStatusPublished: {system.SubmissionStatusWithdrawn},
		system.SubmissionStatusWithdrawn: {system.SubmissionStatusScheduled},
		system.SubmissionStatusRejected:  {system.SubmissionStatusDraft},
	}

	allowedStatuses, exists := validTransitions[oldStatus]
	if !exists {
		return false
	}

	for _, status := range allowedStatuses {
		if status == newStatus {
			return true
		}
	}

	return false
}
