package api

import (
	"github.com/gin-gonic/gin"

	"api-server/api/app/system/department"
	"api-server/api/app/system/menu"
	"api-server/api/app/system/role"
	"api-server/api/app/system/user"
	"api-server/api/app/submission"
	"api-server/api/middleware"
)

func systemRouter(router *gin.RouterGroup) {
	systemRouter := router.Group("/admin/system")
	{
		systemRouter.GET("/user/login/captcha", user.GetCaptcha)
		systemRouter.POST("/user/login", user.Login)
		systemRouter.GET("/login/log", middleware.TokenVerify, user.FindLoginLogList)
		systemRouter.GET("/user/info", middleware.TokenVerify, user.GetUserInfo)
		systemRouter.PUT("/user/info", middleware.TokenVerify, user.UpdateUserInfo)
		systemRouter.GET("/user/menu", middleware.TokenVerify, user.GetUserMenuList)
		systemRouter.GET("/menu", middleware.TokenVerify, menu.GetMenuList)
		systemRouter.POST("/menu", middleware.TokenVerify, menu.AddMenu)
		systemRouter.DELETE("/menu", middleware.TokenVerify, menu.DeleteMenu)
		systemRouter.PUT("/menu", middleware.TokenVerify, menu.UpdateMenu)
		systemRouter.GET("/menu/auth", middleware.TokenVerify, menu.GetMenuAuthList)
		systemRouter.POST("/menu/auth", middleware.TokenVerify, menu.AddMenuAuth)
		systemRouter.DELETE("/menu/auth", middleware.TokenVerify, menu.DeleteMenuAuth)
		systemRouter.PUT("/menu/auth", middleware.TokenVerify, menu.UpdateMenuAuth)
		systemRouter.GET("/menu/role", middleware.TokenVerify, menu.GetMenuListByRoleID)
		systemRouter.PUT("/menu/role", middleware.TokenVerify, menu.UpdateMenuListByRoleID)
		systemRouter.GET("/department", middleware.TokenVerify, department.GetDepartmentList)
		systemRouter.POST("/department", middleware.TokenVerify, department.AddDepartment)
		systemRouter.PUT("/department", middleware.TokenVerify, department.UpdateDepartment)
		systemRouter.DELETE("/department", middleware.TokenVerify, department.DeleteDepartment)
		systemRouter.GET("/role", middleware.TokenVerify, role.GetRoleList)
		systemRouter.POST("/role", middleware.TokenVerify, role.AddRole)
		systemRouter.PUT("/role", middleware.TokenVerify, role.UpdateRole)
		systemRouter.DELETE("/role", middleware.TokenVerify, role.DeleteRole)
		systemRouter.GET("/user", middleware.TokenVerify, user.FindUser)
		systemRouter.GET("/user/cache", middleware.TokenVerify, user.FindUserByCache)
		systemRouter.POST("/user", middleware.TokenVerify, user.AddUser)
		systemRouter.PUT("/user", middleware.TokenVerify, user.UpdateUser)
		systemRouter.DELETE("/user", middleware.TokenVerify, user.DeleteUser)
	}
}

// submissionRouter 投稿管理路由
func submissionRouter(router *gin.RouterGroup) {
	submissionRouter := router.Group("/admin/submission")
	{
		// 投稿管理
		submissionRouter.GET("/list", middleware.TokenVerify, submission.GetSubmissionList)
		submissionRouter.GET("/detail", middleware.TokenVerify, submission.GetSubmissionDetail)
		submissionRouter.POST("/create", middleware.TokenVerify, submission.CreateSubmission)
		submissionRouter.PUT("/update", middleware.TokenVerify, submission.UpdateSubmission)
		submissionRouter.DELETE("/delete", middleware.TokenVerify, submission.DeleteSubmission)
		submissionRouter.PUT("/submit", middleware.TokenVerify, submission.SubmitSubmission)
		submissionRouter.GET("/my", middleware.TokenVerify, submission.GetMySubmissions)
		submissionRouter.GET("/statistics", middleware.TokenVerify, submission.GetSubmissionStatistics)

		// 状态管理
		submissionRouter.PUT("/status", middleware.TokenVerify, submission.UpdateSubmissionStatus)
		submissionRouter.PUT("/schedule", middleware.TokenVerify, submission.ScheduleSubmission)
		submissionRouter.PUT("/publish", middleware.TokenVerify, submission.PublishSubmission)
		submissionRouter.PUT("/withdraw", middleware.TokenVerify, submission.WithdrawSubmission)
		submissionRouter.GET("/logs", middleware.TokenVerify, submission.GetSubmissionLogs)
		submissionRouter.PUT("/batch-status", middleware.TokenVerify, submission.BatchUpdateStatus)

		// 文件管理
		submissionRouter.POST("/file/upload", middleware.TokenVerify, submission.UploadFile)
		submissionRouter.GET("/file/list", middleware.TokenVerify, submission.GetFileList)
		submissionRouter.DELETE("/file/delete", middleware.TokenVerify, submission.DeleteFile)
		submissionRouter.GET("/file/download", middleware.TokenVerify, submission.DownloadFile)
		submissionRouter.PUT("/file/sort", middleware.TokenVerify, submission.UpdateFileSortOrder)
		submissionRouter.GET("/file/statistics", middleware.TokenVerify, submission.GetFileStatistics)

		// 分类管理
		submissionRouter.GET("/category/list", middleware.TokenVerify, submission.GetCategoryList)
		submissionRouter.GET("/category/active", middleware.TokenVerify, submission.GetAllActiveCategories)
		submissionRouter.POST("/category/create", middleware.TokenVerify, submission.CreateCategory)
		submissionRouter.PUT("/category/update", middleware.TokenVerify, submission.UpdateCategory)
		submissionRouter.DELETE("/category/delete", middleware.TokenVerify, submission.DeleteCategory)
		submissionRouter.PUT("/category/sort", middleware.TokenVerify, submission.UpdateCategorySortOrder)

		// 发布日历管理
		submissionRouter.GET("/calendar", middleware.TokenVerify, submission.GetPublishCalendar)
		submissionRouter.PUT("/calendar/update", middleware.TokenVerify, submission.UpdatePublishCalendar)
		submissionRouter.GET("/calendar/available", middleware.TokenVerify, submission.GetAvailablePublishDates)
		submissionRouter.GET("/calendar/statistics", middleware.TokenVerify, submission.GetCalendarStatistics)
		submissionRouter.PUT("/calendar/holiday", middleware.TokenVerify, submission.SetHolidayDates)
		submissionRouter.GET("/calendar/submissions", middleware.TokenVerify, submission.GetSubmissionsByDate)
	}
}

// InitApi init gshop app
func InitApi() *gin.Engine {
	// gin.Default uses Use by default. Two global middlewares are added, Logger(), Recovery(), Logger is to print logs, Recovery is panic and returns 500
	gin.SetMode(gin.ReleaseMode)
	router := gin.Default()
	// https://pkg.go.dev/github.com/gin-gonic/gin#readme-don-t-trust-all-proxies
	router.SetTrustedProxies(nil)
	// Add consent cross-domain middleware
	router.Use(middleware.CorssDomainHandler())
	// static
	router.Static("/static", "./static")
	// api-v1
	v1 := router.Group("/api/v1")
	{
		systemRouter(v1)
		submissionRouter(v1)
	}
	return router
}
