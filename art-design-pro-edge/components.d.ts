/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    ArtBackToTop: typeof import('./src/components/core/base/art-back-to-top/index.vue')['default']
    ArtBreadcrumb: typeof import('./src/components/core/layouts/art-breadcrumb/index.vue')['default']
    ArtButtonMore: typeof import('./src/components/core/forms/ArtButtonMore.vue')['default']
    ArtButtonTable: typeof import('./src/components/core/forms/ArtButtonTable.vue')['default']
    ArtDragVerify: typeof import('./src/components/core/forms/ArtDragVerify.vue')['default']
    ArtExcelExport: typeof import('./src/components/core/forms/ArtExcelExport.vue')['default']
    ArtExcelImport: typeof import('./src/components/core/forms/ArtExcelImport.vue')['default']
    ArtFormInput: typeof import('./src/components/core/forms/ArtFormInput.vue')['default']
    ArtFormSelect: typeof import('./src/components/core/forms/ArtFormSelect.vue')['default']
    ArtGlobalSearch: typeof import('./src/components/core/layouts/art-global-search/index.vue')['default']
    ArtHeaderBar: typeof import('./src/components/core/layouts/art-header-bar/index.vue')['default']
    ArtHorizontalMenu: typeof import('./src/components/core/layouts/art-menus/art-horizontal-menu/index.vue')['default']
    ArtIconSelector: typeof import('./src/components/core/base/art-icon-selector/index.vue')['default']
    ArtMenuRight: typeof import('./src/components/core/others/ArtMenuRight.vue')['default']
    ArtMixedMenu: typeof import('./src/components/core/layouts/art-menus/art-mixed-menu/index.vue')['default']
    ArtPageContent: typeof import('./src/components/core/layouts/art-page-content/index.vue')['default']
    ArtScreenLock: typeof import('./src/components/core/layouts/art-screen-lock/index.vue')['default']
    ArtSettingsPanel: typeof import('./src/components/core/layouts/art-settings-panel/index.vue')['default']
    ArtSidebarMenu: typeof import('./src/components/core/layouts/art-menus/art-sidebar-menu/index.vue')['default']
    ArtTable: typeof import('./src/components/core/tables/ArtTable.vue')['default']
    ArtTableBar: typeof import('./src/components/core/tables/ArtTableBar.vue')['default']
    LoginLeftView: typeof import('./src/components/core/views/login/LoginLeftView.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SidebarSubmenu: typeof import('./src/components/core/layouts/art-menus/art-sidebar-menu/widget/SidebarSubmenu.vue')['default']
  }
}