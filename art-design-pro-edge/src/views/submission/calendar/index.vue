<template>
  <div class="calendar-page">
    <ElRow :gutter="20">
      <!-- 日历主体 -->
      <ElCol :span="18">
        <ElCard shadow="never">
          <template #header>
            <div class="calendar-header">
              <h3>发布日历</h3>
              <div class="calendar-controls">
                <ElButton @click="goToToday">今天</ElButton>
                <ElButton @click="prevMonth">上月</ElButton>
                <ElButton @click="nextMonth">下月</ElButton>
                <ElDatePicker
                  v-model="currentDate"
                  type="month"
                  placeholder="选择月份"
                  format="YYYY年MM月"
                  value-format="YYYY-MM"
                  @change="handleMonthChange"
                />
              </div>
            </div>
          </template>

          <ElCalendar v-model="currentDate" class="submission-calendar">
            <template #date-cell="{ data }">
              <div class="calendar-cell" :class="getCellClass(data)">
                <div class="date-number">{{ data.day.split('-').pop() }}</div>
                <div class="date-info" v-if="getDateInfo(data.day)">
                  <div class="submission-count">
                    {{ getDateInfo(data.day).current }}/{{ getDateInfo(data.day).max }}
                  </div>
                  <div class="submission-status">
                    <ElTag
                      v-for="status in getDateSubmissions(data.day)"
                      :key="status.type"
                      :type="status.tagType"
                      size="small"
                      class="status-tag"
                    >
                      {{ status.count }}
                    </ElTag>
                  </div>
                </div>
                <div class="date-actions" v-if="isWorkDay(data.day)">
                  <ElButton
                    size="small"
                    @click="viewDateDetail(data.day)"
                    class="action-btn"
                  >
                    详情
                  </ElButton>
                </div>
              </div>
            </template>
          </ElCalendar>
        </ElCard>
      </ElCol>

      <!-- 侧边栏 -->
      <ElCol :span="6">
        <!-- 统计信息 -->
        <ElCard shadow="never" class="stats-card">
          <template #header>
            <h4>本月统计</h4>
          </template>
          <div class="stats-grid">
            <div class="stat-item">
              <div class="stat-value">{{ monthStats.totalDays }}</div>
              <div class="stat-label">总天数</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ monthStats.workDays }}</div>
              <div class="stat-label">工作日</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ monthStats.scheduledDays }}</div>
              <div class="stat-label">已安排</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ monthStats.totalScheduled }}</div>
              <div class="stat-label">总投稿</div>
            </div>
          </div>
        </ElCard>

        <!-- 图例 -->
        <ElCard shadow="never" class="legend-card">
          <template #header>
            <h4>图例说明</h4>
          </template>
          <div class="legend-list">
            <div class="legend-item">
              <div class="legend-color today"></div>
              <span>今天</span>
            </div>
            <div class="legend-item">
              <div class="legend-color holiday"></div>
              <span>节假日</span>
            </div>
            <div class="legend-item">
              <div class="legend-color full"></div>
              <span>已满</span>
            </div>
            <div class="legend-item">
              <div class="legend-color available"></div>
              <span>可用</span>
            </div>
          </div>
          
          <div class="status-legend">
            <h5>状态标签</h5>
            <div class="status-list">
              <ElTag type="warning" size="small">待发布</ElTag>
              <ElTag type="success" size="small">已发布</ElTag>
              <ElTag type="primary" size="small">审核中</ElTag>
            </div>
          </div>
        </ElCard>

        <!-- 快速操作 -->
        <ElCard shadow="never" class="actions-card">
          <template #header>
            <h4>快速操作</h4>
          </template>
          <div class="action-buttons">
            <ElButton @click="showHolidayDialog" block>设置节假日</ElButton>
            <ElButton @click="showBatchScheduleDialog" type="primary" block>
              批量安排
            </ElButton>
            <ElButton @click="exportCalendar" block>导出日历</ElButton>
          </div>
        </ElCard>
      </ElCol>
    </ElRow>

    <!-- 日期详情对话框 -->
    <ElDialog
      v-model="detailVisible"
      :title="`${selectedDate} 发布详情`"
      width="800px"
      align-center
    >
      <div v-if="dateDetail" class="date-detail">
        <ElDescriptions :column="2" border>
          <ElDescriptionsItem label="日期">{{ selectedDate }}</ElDescriptionsItem>
          <ElDescriptionsItem label="类型">
            <ElTag :type="dateDetail.is_holiday ? 'danger' : 'success'">
              {{ dateDetail.is_holiday ? '节假日' : '工作日' }}
            </ElTag>
          </ElDescriptionsItem>
          <ElDescriptionsItem label="投稿数量">
            {{ dateDetail.current_submissions }}/{{ dateDetail.max_submissions }}
          </ElDescriptionsItem>
          <ElDescriptionsItem label="剩余容量">
            {{ dateDetail.max_submissions - dateDetail.current_submissions }}
          </ElDescriptionsItem>
        </ElDescriptions>

        <div class="detail-section">
          <h4>投稿列表</h4>
          <ElTable :data="dateSubmissions" stripe>
            <ElTableColumn prop="title" label="标题" min-width="200" />
            <ElTableColumn prop="type" label="类型" width="100">
              <template #default="{ row }">
                <ElTag :type="getTypeTagType(row.type)">
                  {{ getTypeText(row.type) }}
                </ElTag>
              </template>
            </ElTableColumn>
            <ElTableColumn prop="status" label="状态" width="120">
              <template #default="{ row }">
                <ElTag :type="getStatusTagType(row.status)">
                  {{ getStatusText(row.status) }}
                </ElTag>
              </template>
            </ElTableColumn>
            <ElTableColumn prop="user.name" label="投稿人" width="120" />
            <ElTableColumn label="操作" width="120">
              <template #default="{ row }">
                <ElButton size="small" @click="viewSubmission(row)">查看</ElButton>
              </template>
            </ElTableColumn>
          </ElTable>
        </div>

        <div class="detail-section">
          <h4>日历设置</h4>
          <ElForm :model="calendarForm" label-width="100px">
            <ElFormItem label="最大投稿数">
              <ElInputNumber
                v-model="calendarForm.max_submissions"
                :min="1"
                :max="20"
              />
            </ElFormItem>
            <ElFormItem label="是否节假日">
              <ElSwitch v-model="calendarForm.is_holiday" />
            </ElFormItem>
            <ElFormItem label="备注">
              <ElInput
                v-model="calendarForm.notes"
                type="textarea"
                :rows="3"
                placeholder="请输入备注信息"
              />
            </ElFormItem>
            <ElFormItem>
              <ElButton type="primary" @click="updateCalendar">保存设置</ElButton>
            </ElFormItem>
          </ElForm>
        </div>
      </div>
    </ElDialog>

    <!-- 设置节假日对话框 -->
    <ElDialog
      v-model="holidayVisible"
      title="设置节假日"
      width="600px"
      align-center
    >
      <ElForm :model="holidayForm" label-width="100px">
        <ElFormItem label="选择日期">
          <ElDatePicker
            v-model="holidayForm.dates"
            type="dates"
            placeholder="选择多个日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </ElFormItem>
        <ElFormItem label="操作类型">
          <ElRadioGroup v-model="holidayForm.is_holiday">
            <ElRadio :label="true">设为节假日</ElRadio>
            <ElRadio :label="false">设为工作日</ElRadio>
          </ElRadioGroup>
        </ElFormItem>
      </ElForm>
      <template #footer>
        <div class="dialog-footer">
          <ElButton @click="holidayVisible = false">取消</ElButton>
          <ElButton type="primary" @click="setHolidays">确定</ElButton>
        </div>
      </template>
    </ElDialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import {
  getPublishCalendar,
  updatePublishCalendar,
  getCalendarStatistics,
  setHolidayDates,
  getSubmissionsByDate
} from '@/api/submission/api'

defineOptions({ name: 'SubmissionCalendar' })

// 响应式数据
const currentDate = ref(new Date())
const detailVisible = ref(false)
const holidayVisible = ref(false)
const selectedDate = ref('')
const calendarData = ref<any>({})
const submissionData = ref<any>({})
const dateDetail = ref<any>(null)
const dateSubmissions = ref([])

// 统计数据
const monthStats = reactive({
  totalDays: 0,
  workDays: 0,
  scheduledDays: 0,
  totalScheduled: 0
})

// 表单数据
const calendarForm = reactive({
  max_submissions: 5,
  is_holiday: false,
  notes: ''
})

const holidayForm = reactive({
  dates: [],
  is_holiday: true
})

// 计算属性
const currentMonth = computed(() => {
  const date = new Date(currentDate.value)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`
})

// 方法
const loadCalendarData = async () => {
  try {
    const startDate = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth(), 1)
    const endDate = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth() + 1, 0)
    
    const response = await getPublishCalendar({
      start_date: startDate.toISOString().split('T')[0],
      end_date: endDate.toISOString().split('T')[0]
    })
    
    calendarData.value = {}
    submissionData.value = {}
    
    // 处理日历数据
    response.data.calendars.forEach((calendar: any) => {
      calendarData.value[calendar.publish_date] = calendar
    })
    
    // 处理投稿数据
    response.data.submissions.forEach((submission: any) => {
      const date = submission.publish_date
      if (!submissionData.value[date]) {
        submissionData.value[date] = []
      }
      submissionData.value[date].push(submission)
    })
    
    // 加载统计数据
    loadMonthStats()
  } catch (error) {
    console.error('加载日历数据失败:', error)
    ElMessage.error('加载日历数据失败')
  }
}

const loadMonthStats = async () => {
  try {
    const startDate = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth(), 1)
    const endDate = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth() + 1, 0)
    
    const response = await getCalendarStatistics({
      start_date: startDate.toISOString().split('T')[0],
      end_date: endDate.toISOString().split('T')[0]
    })
    
    Object.assign(monthStats, response.data)
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

const getDateInfo = (date: string) => {
  return calendarData.value[date]
}

const getDateSubmissions = (date: string) => {
  const submissions = submissionData.value[date] || []
  const statusCount = {
    scheduled: 0,
    published: 0,
    reviewing: 0
  }
  
  submissions.forEach((submission: any) => {
    if (submission.status === 'scheduled') statusCount.scheduled++
    else if (submission.status === 'published') statusCount.published++
    else if (submission.status === 'reviewing') statusCount.reviewing++
  })
  
  const result = []
  if (statusCount.scheduled > 0) {
    result.push({ type: 'scheduled', count: statusCount.scheduled, tagType: 'warning' })
  }
  if (statusCount.published > 0) {
    result.push({ type: 'published', count: statusCount.published, tagType: 'success' })
  }
  if (statusCount.reviewing > 0) {
    result.push({ type: 'reviewing', count: statusCount.reviewing, tagType: 'primary' })
  }
  
  return result
}

const getCellClass = (data: any) => {
  const date = data.day
  const today = new Date().toISOString().split('T')[0]
  const dateInfo = getDateInfo(date)
  
  const classes = []
  
  if (date === today) classes.push('is-today')
  if (dateInfo?.is_holiday) classes.push('is-holiday')
  if (dateInfo && dateInfo.current_submissions >= dateInfo.max_submissions) classes.push('is-full')
  if (dateInfo && dateInfo.current_submissions > 0) classes.push('has-submissions')
  
  return classes
}

const isWorkDay = (date: string) => {
  const dateInfo = getDateInfo(date)
  return !dateInfo?.is_holiday
}

const viewDateDetail = async (date: string) => {
  try {
    selectedDate.value = date
    dateDetail.value = getDateInfo(date) || {
      publish_date: date,
      max_submissions: 5,
      current_submissions: 0,
      is_holiday: false,
      notes: ''
    }
    
    // 加载当天的投稿列表
    const response = await getSubmissionsByDate({ date, status: '' })
    dateSubmissions.value = response.data
    
    // 设置表单数据
    Object.assign(calendarForm, {
      max_submissions: dateDetail.value.max_submissions,
      is_holiday: dateDetail.value.is_holiday,
      notes: dateDetail.value.notes
    })
    
    detailVisible.value = true
  } catch (error) {
    console.error('加载日期详情失败:', error)
    ElMessage.error('加载日期详情失败')
  }
}

const updateCalendar = async () => {
  try {
    await updatePublishCalendar({
      date: selectedDate.value,
      ...calendarForm
    })
    
    ElMessage.success('日历设置更新成功')
    detailVisible.value = false
    loadCalendarData()
  } catch (error) {
    console.error('更新日历设置失败:', error)
    ElMessage.error('更新日历设置失败')
  }
}

const setHolidays = async () => {
  try {
    if (!holidayForm.dates.length) {
      ElMessage.warning('请选择日期')
      return
    }
    
    await setHolidayDates({
      dates: holidayForm.dates,
      is_holiday: holidayForm.is_holiday
    })
    
    ElMessage.success('节假日设置成功')
    holidayVisible.value = false
    holidayForm.dates = []
    loadCalendarData()
  } catch (error) {
    console.error('设置节假日失败:', error)
    ElMessage.error('设置节假日失败')
  }
}

const showHolidayDialog = () => {
  holidayVisible.value = true
}

const showBatchScheduleDialog = () => {
  ElMessage.info('批量安排功能开发中...')
}

const exportCalendar = () => {
  ElMessage.info('导出功能开发中...')
}

const goToToday = () => {
  currentDate.value = new Date()
}

const prevMonth = () => {
  const date = new Date(currentDate.value)
  date.setMonth(date.getMonth() - 1)
  currentDate.value = date
}

const nextMonth = () => {
  const date = new Date(currentDate.value)
  date.setMonth(date.getMonth() + 1)
  currentDate.value = date
}

const handleMonthChange = () => {
  loadCalendarData()
}

const viewSubmission = (submission: any) => {
  // TODO: 跳转到投稿详情页面
  console.log('查看投稿:', submission)
}

const getTypeText = (type: string) => {
  const typeMap = {
    article: '图文文章',
    video: '视频',
    mixed: '混合媒体'
  }
  return typeMap[type] || type
}

const getTypeTagType = (type: string) => {
  const typeMap = {
    article: '',
    video: 'success',
    mixed: 'warning'
  }
  return typeMap[type] || ''
}

const getStatusText = (status: string) => {
  const statusMap = {
    scheduled: '待发布',
    published: '已发布',
    reviewing: '审核中'
  }
  return statusMap[status] || status
}

const getStatusTagType = (status: string) => {
  const statusMap = {
    scheduled: 'warning',
    published: 'success',
    reviewing: 'primary'
  }
  return statusMap[status] || ''
}

// 监听日期变化
watch(currentDate, () => {
  loadCalendarData()
})

// 生命周期
onMounted(() => {
  loadCalendarData()
})
</script>

<style lang="scss" scoped>
.calendar-page {
  .calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    h3 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
    }
    
    .calendar-controls {
      display: flex;
      gap: 8px;
      align-items: center;
    }
  }
  
  .submission-calendar {
    :deep(.el-calendar__body) {
      padding: 12px;
    }
    
    :deep(.el-calendar-table) {
      .el-calendar-day {
        height: 120px;
        padding: 4px;
      }
    }
    
    .calendar-cell {
      height: 100%;
      display: flex;
      flex-direction: column;
      position: relative;
      
      &.is-today {
        background-color: var(--el-color-primary-light-9);
        border: 1px solid var(--el-color-primary);
        border-radius: 4px;
      }
      
      &.is-holiday {
        background-color: var(--el-color-danger-light-9);
      }
      
      &.is-full {
        background-color: var(--el-color-warning-light-9);
      }
      
      &.has-submissions {
        background-color: var(--el-color-success-light-9);
      }
      
      .date-number {
        font-weight: 600;
        margin-bottom: 4px;
      }
      
      .date-info {
        flex: 1;
        
        .submission-count {
          font-size: 12px;
          color: var(--el-text-color-secondary);
          margin-bottom: 4px;
        }
        
        .submission-status {
          display: flex;
          flex-wrap: wrap;
          gap: 2px;
          
          .status-tag {
            font-size: 10px;
            padding: 0 4px;
            height: 16px;
            line-height: 16px;
          }
        }
      }
      
      .date-actions {
        .action-btn {
          font-size: 10px;
          padding: 2px 6px;
          height: 20px;
        }
      }
    }
  }
  
  .stats-card,
  .legend-card,
  .actions-card {
    margin-bottom: 16px;
    
    h4 {
      margin: 0;
      font-size: 14px;
      font-weight: 600;
    }
  }
  
  .stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    
    .stat-item {
      text-align: center;
      
      .stat-value {
        font-size: 24px;
        font-weight: 600;
        color: var(--el-color-primary);
      }
      
      .stat-label {
        font-size: 12px;
        color: var(--el-text-color-secondary);
        margin-top: 4px;
      }
    }
  }
  
  .legend-list {
    .legend-item {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      
      .legend-color {
        width: 16px;
        height: 16px;
        border-radius: 2px;
        margin-right: 8px;
        
        &.today {
          background-color: var(--el-color-primary-light-9);
          border: 1px solid var(--el-color-primary);
        }
        
        &.holiday {
          background-color: var(--el-color-danger-light-9);
        }
        
        &.full {
          background-color: var(--el-color-warning-light-9);
        }
        
        &.available {
          background-color: var(--el-color-success-light-9);
        }
      }
    }
  }
  
  .status-legend {
    margin-top: 16px;
    
    h5 {
      margin: 0 0 8px 0;
      font-size: 12px;
      color: var(--el-text-color-regular);
    }
    
    .status-list {
      display: flex;
      flex-direction: column;
      gap: 4px;
    }
  }
  
  .action-buttons {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
  
  .date-detail {
    .detail-section {
      margin-top: 20px;
      
      h4 {
        margin-bottom: 12px;
        font-size: 14px;
        color: var(--el-text-color-regular);
      }
    }
  }
}
</style>
