<template>
  <ArtTableFullScreen>
    <div class="submission-page" id="table-full-screen">
      <!-- 搜索栏 -->
      <ArtSearchBar
        v-model:filter="searchForm"
        :items="searchItems"
        @reset="resetSearch"
        @search="search"
      ></ArtSearchBar>

      <ElCard shadow="never" class="art-table-card">
        <!-- 表格头部 -->
        <ArtTableHeader
          :columnList="columnOptions"
          v-model:columns="columnChecks"
          @refresh="handleRefresh"
        >
          <template #left>
            <ElButton @click="showDialog('add')" v-ripple>新建投稿</ElButton>
            <ElButton 
              @click="batchApprove" 
              :disabled="!selectedRows.length"
              type="success"
            >
              批量通过
            </ElButton>
            <ElButton 
              @click="batchReject" 
              :disabled="!selectedRows.length"
              type="danger"
            >
              批量拒绝
            </ElButton>
          </template>
        </ArtTableHeader>

        <!-- 表格 -->
        <ArtTable
          ref="tableRef"
          row-key="id"
          :loading="loading"
          :data="tableData"
          :currentPage="pagination.currentPage"
          :pageSize="pagination.pageSize"
          :total="pagination.total"
          :marginTop="10"
          @selection-change="handleSelectionChange"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
          <template #default>
            <ElTableColumn type="selection" width="55" />
            <ElTableColumn prop="id" label="ID" width="80" />
            <ElTableColumn prop="title" label="标题" min-width="200" show-overflow-tooltip />
            <ElTableColumn prop="type" label="类型" width="100">
              <template #default="{ row }">
                <ElTag :type="getTypeTagType(row.type)">
                  {{ getTypeText(row.type) }}
                </ElTag>
              </template>
            </ElTableColumn>
            <ElTableColumn prop="status" label="状态" width="120">
              <template #default="{ row }">
                <ElTag :type="getStatusTagType(row.status)">
                  {{ getStatusText(row.status) }}
                </ElTag>
              </template>
            </ElTableColumn>
            <ElTableColumn prop="user.name" label="投稿人" width="120" />
            <ElTableColumn prop="priority" label="优先级" width="100">
              <template #default="{ row }">
                <ElTag :type="getPriorityTagType(row.priority)" size="small">
                  {{ getPriorityText(row.priority) }}
                </ElTag>
              </template>
            </ElTableColumn>
            <ElTableColumn prop="publish_date" label="发布日期" width="120">
              <template #default="{ row }">
                {{ row.publish_date ? formatDate(row.publish_date) : '-' }}
              </template>
            </ElTableColumn>
            <ElTableColumn prop="created_at" label="创建时间" width="160">
              <template #default="{ row }">
                {{ formatDateTime(row.created_at) }}
              </template>
            </ElTableColumn>
            <ElTableColumn label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <ElButton size="small" @click="viewDetail(row)">详情</ElButton>
                <ElButton 
                  size="small" 
                  type="primary" 
                  @click="editSubmission(row)"
                  v-if="canEdit(row)"
                >
                  编辑
                </ElButton>
                <ElDropdown @command="handleCommand($event, row)">
                  <ElButton size="small" type="info">
                    更多<ElIcon><ArrowDown /></ElIcon>
                  </ElButton>
                  <template #dropdown>
                    <ElDropdownMenu>
                      <ElDropdownItem command="approve" v-if="canApprove(row)">
                        通过
                      </ElDropdownItem>
                      <ElDropdownItem command="reject" v-if="canReject(row)">
                        拒绝
                      </ElDropdownItem>
                      <ElDropdownItem command="schedule" v-if="canSchedule(row)">
                        安排发布
                      </ElDropdownItem>
                      <ElDropdownItem command="publish" v-if="canPublish(row)">
                        立即发布
                      </ElDropdownItem>
                      <ElDropdownItem command="withdraw" v-if="canWithdraw(row)">
                        撤回
                      </ElDropdownItem>
                      <ElDropdownItem command="delete" v-if="canDelete(row)" divided>
                        删除
                      </ElDropdownItem>
                    </ElDropdownMenu>
                  </template>
                </ElDropdown>
              </template>
            </ElTableColumn>
          </template>
        </ArtTable>

        <!-- 新建/编辑投稿对话框 -->
        <ElDialog
          v-model="dialogVisible"
          :title="dialogType === 'add' ? '新建投稿' : '编辑投稿'"
          width="600px"
          align-center
        >
          <ElForm ref="formRef" :model="formData" :rules="rules" label-width="100px">
            <ElFormItem label="投稿标题" prop="title">
              <ElInput v-model="formData.title" placeholder="请输入投稿标题" />
            </ElFormItem>
            <ElFormItem label="投稿类型" prop="type">
              <ElSelect v-model="formData.type" placeholder="请选择投稿类型">
                <ElOption label="图文文章" value="article" />
                <ElOption label="视频" value="video" />
                <ElOption label="混合媒体" value="mixed" />
              </ElSelect>
            </ElFormItem>
            <ElFormItem label="投稿描述" prop="description">
              <ElInput 
                v-model="formData.description" 
                type="textarea" 
                :rows="4"
                placeholder="请输入投稿描述"
              />
            </ElFormItem>
            <ElFormItem label="标签">
              <ElInput v-model="formData.tags" placeholder="多个标签用逗号分隔" />
            </ElFormItem>
            <ElFormItem label="关键词">
              <ElInput v-model="formData.keywords" placeholder="多个关键词用逗号分隔" />
            </ElFormItem>
          </ElForm>
          <template #footer>
            <div class="dialog-footer">
              <ElButton @click="dialogVisible = false">取消</ElButton>
              <ElButton type="primary" @click="handleSubmit" :loading="submitLoading">
                {{ dialogType === 'add' ? '创建' : '更新' }}
              </ElButton>
            </div>
          </template>
        </ElDialog>

        <!-- 状态更新对话框 -->
        <ElDialog
          v-model="statusDialogVisible"
          :title="statusDialogTitle"
          width="400px"
          align-center
        >
          <ElForm :model="statusForm" label-width="80px">
            <ElFormItem label="备注">
              <ElInput 
                v-model="statusForm.comment" 
                type="textarea" 
                :rows="3"
                placeholder="请输入操作备注"
              />
            </ElFormItem>
            <ElFormItem label="发布日期" v-if="statusForm.action === 'schedule'">
              <ElDatePicker
                v-model="statusForm.publishDate"
                type="date"
                placeholder="选择发布日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </ElFormItem>
          </ElForm>
          <template #footer>
            <div class="dialog-footer">
              <ElButton @click="statusDialogVisible = false">取消</ElButton>
              <ElButton type="primary" @click="confirmStatusUpdate" :loading="statusLoading">
                确认
              </ElButton>
            </div>
          </template>
        </ElDialog>
      </ElCard>
    </div>
  </ArtTableFullScreen>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox, FormInstance } from 'element-plus'
import { ArrowDown } from '@element-plus/icons-vue'
import type { FormRules } from 'element-plus'
import { useCheckedColumns } from '@/composables/useCheckedColumns'
import { SearchFormItem } from '@/types'

defineOptions({ name: 'SubmissionList' })

// 响应式数据
const loading = ref(false)
const submitLoading = ref(false)
const statusLoading = ref(false)
const dialogVisible = ref(false)
const statusDialogVisible = ref(false)
const dialogType = ref('add')
const tableData = ref([])
const selectedRows = ref([])
const formRef = ref<FormInstance>()

// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 搜索表单
const searchForm = reactive({
  title: '',
  status: '',
  type: '',
  user_id: ''
})

// 表单数据
const formData = reactive({
  id: null,
  title: '',
  type: '',
  description: '',
  tags: '',
  keywords: ''
})

// 状态更新表单
const statusForm = reactive({
  action: '',
  comment: '',
  publishDate: ''
})

// 搜索项配置
const searchItems: SearchFormItem[] = [
  {
    label: '标题',
    prop: 'title',
    type: 'input',
    placeholder: '请输入投稿标题'
  },
  {
    label: '状态',
    prop: 'status',
    type: 'select',
    options: [
      { label: '草稿', value: 'draft' },
      { label: '已提交', value: 'submitted' },
      { label: '审核中', value: 'reviewing' },
      { label: '已通过', value: 'approved' },
      { label: '已拒绝', value: 'rejected' },
      { label: '需修改', value: 'revision' },
      { label: '待发布', value: 'scheduled' },
      { label: '已发布', value: 'published' },
      { label: '已撤回', value: 'withdrawn' }
    ]
  },
  {
    label: '类型',
    prop: 'type',
    type: 'select',
    options: [
      { label: '图文文章', value: 'article' },
      { label: '视频', value: 'video' },
      { label: '混合媒体', value: 'mixed' }
    ]
  }
]

// 表单验证规则
const rules: FormRules = {
  title: [
    { required: true, message: '请输入投稿标题', trigger: 'blur' },
    { min: 2, max: 100, message: '标题长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择投稿类型', trigger: 'change' }
  ],
  description: [
    { max: 500, message: '描述不能超过 500 个字符', trigger: 'blur' }
  ]
}

// 列配置
const columnOptions = [
  { label: 'ID', prop: 'id' },
  { label: '标题', prop: 'title' },
  { label: '类型', prop: 'type' },
  { label: '状态', prop: 'status' },
  { label: '投稿人', prop: 'user' },
  { label: '优先级', prop: 'priority' },
  { label: '发布日期', prop: 'publish_date' },
  { label: '创建时间', prop: 'created_at' }
]

const { columnChecks } = useCheckedColumns(columnOptions)

// 计算属性
const statusDialogTitle = computed(() => {
  const actionMap = {
    approve: '通过投稿',
    reject: '拒绝投稿',
    schedule: '安排发布',
    publish: '立即发布',
    withdraw: '撤回投稿'
  }
  return actionMap[statusForm.action] || '更新状态'
})

// 方法
const getTypeText = (type: string) => {
  const typeMap = {
    article: '图文文章',
    video: '视频',
    mixed: '混合媒体'
  }
  return typeMap[type] || type
}

const getTypeTagType = (type: string) => {
  const typeMap = {
    article: '',
    video: 'success',
    mixed: 'warning'
  }
  return typeMap[type] || ''
}

const getStatusText = (status: string) => {
  const statusMap = {
    draft: '草稿',
    submitted: '已提交',
    reviewing: '审核中',
    approved: '已通过',
    rejected: '已拒绝',
    revision: '需修改',
    scheduled: '待发布',
    published: '已发布',
    withdrawn: '已撤回'
  }
  return statusMap[status] || status
}

const getStatusTagType = (status: string) => {
  const statusMap = {
    draft: 'info',
    submitted: 'warning',
    reviewing: 'primary',
    approved: 'success',
    rejected: 'danger',
    revision: 'warning',
    scheduled: 'primary',
    published: 'success',
    withdrawn: 'info'
  }
  return statusMap[status] || ''
}

const getPriorityText = (priority: string) => {
  const priorityMap = {
    low: '低',
    normal: '普通',
    high: '高',
    urgent: '紧急'
  }
  return priorityMap[priority] || priority
}

const getPriorityTagType = (priority: string) => {
  const priorityMap = {
    low: 'info',
    normal: '',
    high: 'warning',
    urgent: 'danger'
  }
  return priorityMap[priority] || ''
}

const formatDate = (timestamp: number) => {
  return new Date(timestamp * 1000).toLocaleDateString()
}

const formatDateTime = (timestamp: number) => {
  return new Date(timestamp * 1000).toLocaleString()
}

// 权限检查方法
const canEdit = (row: any) => {
  return ['draft', 'revision'].includes(row.status)
}

const canApprove = (row: any) => {
  return ['submitted', 'reviewing'].includes(row.status)
}

const canReject = (row: any) => {
  return ['submitted', 'reviewing', 'approved'].includes(row.status)
}

const canSchedule = (row: any) => {
  return row.status === 'approved'
}

const canPublish = (row: any) => {
  return row.status === 'scheduled'
}

const canWithdraw = (row: any) => {
  return ['submitted', 'scheduled', 'published'].includes(row.status)
}

const canDelete = (row: any) => {
  return row.status === 'draft'
}

// 事件处理方法
const handleRefresh = () => {
  fetchData()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    title: '',
    status: '',
    type: '',
    user_id: ''
  })
  pagination.currentPage = 1
  fetchData()
}

const search = () => {
  pagination.currentPage = 1
  fetchData()
}

const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  fetchData()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  fetchData()
}

const showDialog = (type: string, row?: any) => {
  dialogType.value = type
  if (type === 'add') {
    Object.assign(formData, {
      id: null,
      title: '',
      type: '',
      description: '',
      tags: '',
      keywords: ''
    })
  } else {
    Object.assign(formData, row)
  }
  dialogVisible.value = true
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    submitLoading.value = true
    
    // TODO: 调用API
    if (dialogType.value === 'add') {
      // await createSubmission(formData)
      ElMessage.success('创建成功')
    } else {
      // await updateSubmission(formData)
      ElMessage.success('更新成功')
    }
    
    dialogVisible.value = false
    fetchData()
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    submitLoading.value = false
  }
}

const viewDetail = (row: any) => {
  // TODO: 跳转到详情页面
  console.log('查看详情:', row)
}

const editSubmission = (row: any) => {
  showDialog('edit', row)
}

const handleCommand = (command: string, row: any) => {
  statusForm.action = command
  statusForm.comment = ''
  statusForm.publishDate = ''
  
  if (command === 'delete') {
    handleDelete(row)
  } else {
    statusDialogVisible.value = true
  }
}

const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm('确定要删除这个投稿吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    // TODO: 调用删除API
    // await deleteSubmission(row.id)
    ElMessage.success('删除成功')
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
    }
  }
}

const confirmStatusUpdate = async () => {
  try {
    statusLoading.value = true
    
    // TODO: 调用状态更新API
    // await updateSubmissionStatus(statusForm)
    ElMessage.success('状态更新成功')
    
    statusDialogVisible.value = false
    fetchData()
  } catch (error) {
    console.error('状态更新失败:', error)
  } finally {
    statusLoading.value = false
  }
}

const batchApprove = async () => {
  if (!selectedRows.value.length) return
  
  try {
    await ElMessageBox.confirm(`确定要批量通过选中的 ${selectedRows.value.length} 个投稿吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    // TODO: 调用批量通过API
    ElMessage.success('批量通过成功')
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量通过失败:', error)
    }
  }
}

const batchReject = async () => {
  if (!selectedRows.value.length) return
  
  try {
    await ElMessageBox.confirm(`确定要批量拒绝选中的 ${selectedRows.value.length} 个投稿吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    // TODO: 调用批量拒绝API
    ElMessage.success('批量拒绝成功')
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量拒绝失败:', error)
    }
  }
}

const fetchData = async () => {
  try {
    loading.value = true
    
    // TODO: 调用API获取数据
    // const params = {
    //   ...searchForm,
    //   page: pagination.currentPage,
    //   page_size: pagination.pageSize
    // }
    // const response = await getSubmissionList(params)
    // tableData.value = response.data
    // pagination.total = response.total
    
    // 模拟数据
    tableData.value = []
    pagination.total = 0
  } catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  fetchData()
})
</script>

<style lang="scss" scoped>
.submission-page {
  height: 100%;
  
  .art-table-card {
    margin-top: 16px;
  }
}
</style>
