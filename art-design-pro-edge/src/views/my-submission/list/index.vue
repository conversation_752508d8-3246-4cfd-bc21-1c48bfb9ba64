<template>
  <ArtTableFullScreen>
    <div class="my-submission-page" id="table-full-screen">
      <!-- 搜索栏 -->
      <ArtSearchBar
        v-model:filter="searchForm"
        :items="searchItems"
        @reset="resetSearch"
        @search="search"
      ></ArtSearchBar>

      <ElCard shadow="never" class="art-table-card">
        <!-- 表格头部 -->
        <ArtTableHeader
          :columnList="columnOptions"
          v-model:columns="columnChecks"
          @refresh="handleRefresh"
        >
          <template #left>
            <ElButton type="primary" @click="createSubmission" v-ripple>
              新建投稿
            </ElButton>
            <ElButton 
              @click="batchDelete" 
              :disabled="!selectedRows.length"
              type="danger"
            >
              批量删除
            </ElButton>
          </template>
          <template #right>
            <div class="statistics-info">
              <ElStatistic title="总投稿数" :value="statistics.total" />
              <ElStatistic title="已发布" :value="statistics.published" />
              <ElStatistic title="审核中" :value="statistics.reviewing" />
            </div>
          </template>
        </ArtTableHeader>

        <!-- 表格 -->
        <ArtTable
          ref="tableRef"
          row-key="id"
          :loading="loading"
          :data="tableData"
          :currentPage="pagination.currentPage"
          :pageSize="pagination.pageSize"
          :total="pagination.total"
          :marginTop="10"
          @selection-change="handleSelectionChange"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
          <template #default>
            <ElTableColumn type="selection" width="55" />
            <ElTableColumn prop="id" label="ID" width="80" />
            <ElTableColumn prop="title" label="标题" min-width="200" show-overflow-tooltip>
              <template #default="{ row }">
                <ElLink @click="viewDetail(row)" type="primary">
                  {{ row.title }}
                </ElLink>
              </template>
            </ElTableColumn>
            <ElTableColumn prop="type" label="类型" width="100">
              <template #default="{ row }">
                <ElTag :type="getTypeTagType(row.type)">
                  {{ getTypeText(row.type) }}
                </ElTag>
              </template>
            </ElTableColumn>
            <ElTableColumn prop="status" label="状态" width="120">
              <template #default="{ row }">
                <ElTag :type="getStatusTagType(row.status)">
                  {{ getStatusText(row.status) }}
                </ElTag>
              </template>
            </ElTableColumn>
            <ElTableColumn prop="priority" label="优先级" width="100">
              <template #default="{ row }">
                <ElTag :type="getPriorityTagType(row.priority)" size="small">
                  {{ getPriorityText(row.priority) }}
                </ElTag>
              </template>
            </ElTableColumn>
            <ElTableColumn prop="publish_date" label="发布日期" width="120">
              <template #default="{ row }">
                {{ row.publish_date ? formatDate(row.publish_date) : '-' }}
              </template>
            </ElTableColumn>
            <ElTableColumn prop="submitted_at" label="提交时间" width="160">
              <template #default="{ row }">
                {{ row.submitted_at ? formatDateTime(row.submitted_at) : '-' }}
              </template>
            </ElTableColumn>
            <ElTableColumn prop="created_at" label="创建时间" width="160">
              <template #default="{ row }">
                {{ formatDateTime(row.created_at) }}
              </template>
            </ElTableColumn>
            <ElTableColumn label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <ElButton size="small" @click="viewDetail(row)">详情</ElButton>
                <ElButton 
                  size="small" 
                  type="primary" 
                  @click="editSubmission(row)"
                  v-if="canEdit(row)"
                >
                  编辑
                </ElButton>
                <ElDropdown @command="handleCommand($event, row)">
                  <ElButton size="small" type="info">
                    更多<ElIcon><ArrowDown /></ElIcon>
                  </ElButton>
                  <template #dropdown>
                    <ElDropdownMenu>
                      <ElDropdownItem command="submit" v-if="canSubmit(row)">
                        提交
                      </ElDropdownItem>
                      <ElDropdownItem command="withdraw" v-if="canWithdraw(row)">
                        撤回
                      </ElDropdownItem>
                      <ElDropdownItem command="copy" divided>
                        复制
                      </ElDropdownItem>
                      <ElDropdownItem command="delete" v-if="canDelete(row)">
                        删除
                      </ElDropdownItem>
                    </ElDropdownMenu>
                  </template>
                </ElDropdown>
              </template>
            </ElTableColumn>
          </template>
        </ArtTable>

        <!-- 投稿详情对话框 -->
        <ElDialog
          v-model="detailVisible"
          title="投稿详情"
          width="80%"
          align-center
        >
          <div v-if="currentSubmission" class="submission-detail">
            <ElDescriptions :column="2" border>
              <ElDescriptionsItem label="投稿标题">
                {{ currentSubmission.title }}
              </ElDescriptionsItem>
              <ElDescriptionsItem label="投稿类型">
                <ElTag :type="getTypeTagType(currentSubmission.type)">
                  {{ getTypeText(currentSubmission.type) }}
                </ElTag>
              </ElDescriptionsItem>
              <ElDescriptionsItem label="状态">
                <ElTag :type="getStatusTagType(currentSubmission.status)">
                  {{ getStatusText(currentSubmission.status) }}
                </ElTag>
              </ElDescriptionsItem>
              <ElDescriptionsItem label="优先级">
                <ElTag :type="getPriorityTagType(currentSubmission.priority)">
                  {{ getPriorityText(currentSubmission.priority) }}
                </ElTag>
              </ElDescriptionsItem>
              <ElDescriptionsItem label="创建时间">
                {{ formatDateTime(currentSubmission.created_at) }}
              </ElDescriptionsItem>
              <ElDescriptionsItem label="提交时间">
                {{ currentSubmission.submitted_at ? formatDateTime(currentSubmission.submitted_at) : '-' }}
              </ElDescriptionsItem>
              <ElDescriptionsItem label="发布日期">
                {{ currentSubmission.publish_date ? formatDate(currentSubmission.publish_date) : '-' }}
              </ElDescriptionsItem>
              <ElDescriptionsItem label="查看次数">
                {{ currentSubmission.view_count || 0 }}
              </ElDescriptionsItem>
            </ElDescriptions>

            <div class="detail-section">
              <h4>投稿描述</h4>
              <p>{{ currentSubmission.description || '暂无描述' }}</p>
            </div>

            <div class="detail-section" v-if="currentSubmission.tags">
              <h4>标签</h4>
              <ElTag
                v-for="tag in currentSubmission.tags.split(',')"
                :key="tag"
                class="tag-item"
              >
                {{ tag.trim() }}
              </ElTag>
            </div>

            <div class="detail-section" v-if="currentSubmission.admin_notes">
              <h4>管理员备注</h4>
              <ElAlert :title="currentSubmission.admin_notes" type="info" :closable="false" />
            </div>

            <div class="detail-section" v-if="currentSubmission.files && currentSubmission.files.length">
              <h4>附件文件</h4>
              <div class="file-list">
                <div
                  v-for="file in currentSubmission.files"
                  :key="file.id"
                  class="file-item"
                >
                  <ElIcon class="file-icon">
                    <Document v-if="file.file_category === 'document'" />
                    <Picture v-else-if="file.file_category === 'image'" />
                    <VideoPlay v-else-if="file.file_category === 'video'" />
                    <Files v-else />
                  </ElIcon>
                  <span class="file-name">{{ file.original_name }}</span>
                  <span class="file-size">{{ formatFileSize(file.file_size) }}</span>
                  <ElButton size="small" @click="downloadFile(file)">下载</ElButton>
                </div>
              </div>
            </div>

            <!-- 操作日志 -->
            <div class="detail-section" v-if="submissionLogs.length">
              <h4>操作日志</h4>
              <ElTimeline>
                <ElTimelineItem
                  v-for="log in submissionLogs"
                  :key="log.id"
                  :timestamp="formatDateTime(log.created_at)"
                >
                  <div class="log-item">
                    <div class="log-action">{{ getActionText(log.action) }}</div>
                    <div class="log-status" v-if="log.old_status && log.new_status">
                      {{ getStatusText(log.old_status) }} → {{ getStatusText(log.new_status) }}
                    </div>
                    <div class="log-comment" v-if="log.comment">{{ log.comment }}</div>
                    <div class="log-user">操作人：{{ log.user?.name || '系统' }}</div>
                  </div>
                </ElTimelineItem>
              </ElTimeline>
            </div>
          </div>
        </ElDialog>
      </ElCard>
    </div>
  </ArtTableFullScreen>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowDown, Document, Picture, VideoPlay, Files } from '@element-plus/icons-vue'
import { useCheckedColumns } from '@/composables/useCheckedColumns'
import { SearchFormItem } from '@/types'
import {
  getMySubmissions,
  getSubmissionDetail,
  getSubmissionStatistics,
  getSubmissionLogs,
  deleteSubmission,
  submitSubmission as submitSubmissionApi,
  withdrawSubmission,
  downloadFile as downloadFileApi
} from '@/api/submission/api'

defineOptions({ name: 'MySubmissionList' })

const router = useRouter()

// 响应式数据
const loading = ref(false)
const detailVisible = ref(false)
const tableData = ref([])
const selectedRows = ref([])
const currentSubmission = ref<any>(null)
const submissionLogs = ref([])
const statistics = ref({
  total: 0,
  published: 0,
  reviewing: 0
})

// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 搜索表单
const searchForm = reactive({
  status: '',
  type: ''
})

// 搜索项配置
const searchItems: SearchFormItem[] = [
  {
    label: '状态',
    prop: 'status',
    type: 'select',
    options: [
      { label: '草稿', value: 'draft' },
      { label: '已提交', value: 'submitted' },
      { label: '审核中', value: 'reviewing' },
      { label: '已通过', value: 'approved' },
      { label: '已拒绝', value: 'rejected' },
      { label: '需修改', value: 'revision' },
      { label: '待发布', value: 'scheduled' },
      { label: '已发布', value: 'published' },
      { label: '已撤回', value: 'withdrawn' }
    ]
  },
  {
    label: '类型',
    prop: 'type',
    type: 'select',
    options: [
      { label: '图文文章', value: 'article' },
      { label: '视频', value: 'video' },
      { label: '混合媒体', value: 'mixed' }
    ]
  }
]

// 列配置
const columnOptions = [
  { label: 'ID', prop: 'id' },
  { label: '标题', prop: 'title' },
  { label: '类型', prop: 'type' },
  { label: '状态', prop: 'status' },
  { label: '优先级', prop: 'priority' },
  { label: '发布日期', prop: 'publish_date' },
  { label: '提交时间', prop: 'submitted_at' },
  { label: '创建时间', prop: 'created_at' }
]

const { columnChecks } = useCheckedColumns(columnOptions)

// 方法
const getTypeText = (type: string) => {
  const typeMap = {
    article: '图文文章',
    video: '视频',
    mixed: '混合媒体'
  }
  return typeMap[type] || type
}

const getTypeTagType = (type: string) => {
  const typeMap = {
    article: '',
    video: 'success',
    mixed: 'warning'
  }
  return typeMap[type] || ''
}

const getStatusText = (status: string) => {
  const statusMap = {
    draft: '草稿',
    submitted: '已提交',
    reviewing: '审核中',
    approved: '已通过',
    rejected: '已拒绝',
    revision: '需修改',
    scheduled: '待发布',
    published: '已发布',
    withdrawn: '已撤回'
  }
  return statusMap[status] || status
}

const getStatusTagType = (status: string) => {
  const statusMap = {
    draft: 'info',
    submitted: 'warning',
    reviewing: 'primary',
    approved: 'success',
    rejected: 'danger',
    revision: 'warning',
    scheduled: 'primary',
    published: 'success',
    withdrawn: 'info'
  }
  return statusMap[status] || ''
}

const getPriorityText = (priority: string) => {
  const priorityMap = {
    low: '低',
    normal: '普通',
    high: '高',
    urgent: '紧急'
  }
  return priorityMap[priority] || priority
}

const getPriorityTagType = (priority: string) => {
  const priorityMap = {
    low: 'info',
    normal: '',
    high: 'warning',
    urgent: 'danger'
  }
  return priorityMap[priority] || ''
}

const getActionText = (action: string) => {
  const actionMap = {
    status_change: '状态变更',
    create: '创建',
    update: '更新',
    submit: '提交',
    withdraw: '撤回'
  }
  return actionMap[action] || action
}

const formatDate = (timestamp: number) => {
  return new Date(timestamp * 1000).toLocaleDateString()
}

const formatDateTime = (timestamp: number) => {
  return new Date(timestamp * 1000).toLocaleString()
}

const formatFileSize = (size: number) => {
  if (size < 1024) return size + ' B'
  if (size < 1024 * 1024) return (size / 1024).toFixed(1) + ' KB'
  return (size / 1024 / 1024).toFixed(1) + ' MB'
}

// 权限检查方法
const canEdit = (row: any) => {
  return ['draft', 'revision'].includes(row.status)
}

const canSubmit = (row: any) => {
  return ['draft', 'revision'].includes(row.status)
}

const canWithdraw = (row: any) => {
  return row.status === 'submitted'
}

const canDelete = (row: any) => {
  return row.status === 'draft'
}

// 事件处理方法
const handleRefresh = () => {
  fetchData()
  loadStatistics()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    status: '',
    type: ''
  })
  pagination.currentPage = 1
  fetchData()
}

const search = () => {
  pagination.currentPage = 1
  fetchData()
}

const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  fetchData()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  fetchData()
}

const createSubmission = () => {
  router.push('/my-submission/create')
}

const editSubmission = (row: any) => {
  router.push(`/my-submission/create?id=${row.id}`)
}

const viewDetail = async (row: any) => {
  try {
    const response = await getSubmissionDetail(row.id)
    currentSubmission.value = response.data
    
    // 加载操作日志
    const logsResponse = await getSubmissionLogs(row.id)
    submissionLogs.value = logsResponse.data
    
    detailVisible.value = true
  } catch (error) {
    console.error('获取投稿详情失败:', error)
    ElMessage.error('获取投稿详情失败')
  }
}

const handleCommand = async (command: string, row: any) => {
  switch (command) {
    case 'submit':
      await handleSubmit(row)
      break
    case 'withdraw':
      await handleWithdraw(row)
      break
    case 'copy':
      await handleCopy(row)
      break
    case 'delete':
      await handleDelete(row)
      break
  }
}

const handleSubmit = async (row: any) => {
  try {
    await ElMessageBox.confirm('确定要提交这个投稿吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await submitSubmissionApi(row.id)
    ElMessage.success('投稿提交成功')
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('提交投稿失败:', error)
      ElMessage.error('提交投稿失败')
    }
  }
}

const handleWithdraw = async (row: any) => {
  try {
    await ElMessageBox.confirm('确定要撤回这个投稿吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await withdrawSubmission({ id: row.id, comment: '用户主动撤回' })
    ElMessage.success('投稿撤回成功')
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('撤回投稿失败:', error)
      ElMessage.error('撤回投稿失败')
    }
  }
}

const handleCopy = (row: any) => {
  router.push(`/my-submission/create?copy=${row.id}`)
}

const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm('确定要删除这个投稿吗？删除后无法恢复！', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await deleteSubmission(row.id)
    ElMessage.success('投稿删除成功')
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除投稿失败:', error)
      ElMessage.error('删除投稿失败')
    }
  }
}

const batchDelete = async () => {
  if (!selectedRows.value.length) return
  
  try {
    await ElMessageBox.confirm(`确定要删除选中的 ${selectedRows.value.length} 个投稿吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    // TODO: 实现批量删除API
    ElMessage.success('批量删除成功')
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

const downloadFile = async (file: any) => {
  try {
    const response = await downloadFileApi(file.id)
    const url = window.URL.createObjectURL(new Blob([response]))
    const link = document.createElement('a')
    link.href = url
    link.download = file.original_name
    link.click()
    window.URL.revokeObjectURL(url)
  } catch (error) {
    console.error('下载文件失败:', error)
    ElMessage.error('下载文件失败')
  }
}

const fetchData = async () => {
  try {
    loading.value = true
    
    const params = {
      ...searchForm,
      page: pagination.currentPage,
      page_size: pagination.pageSize
    }
    
    const response = await getMySubmissions(params)
    tableData.value = response.data
    pagination.total = response.total
  } catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

const loadStatistics = async () => {
  try {
    const response = await getSubmissionStatistics()
    const stats = response.data
    statistics.value = {
      total: stats.total || 0,
      published: stats.status_stats?.published || 0,
      reviewing: (stats.status_stats?.submitted || 0) + (stats.status_stats?.reviewing || 0)
    }
  } catch (error) {
    console.error('获取统计信息失败:', error)
  }
}

// 生命周期
onMounted(() => {
  fetchData()
  loadStatistics()
})
</script>

<style lang="scss" scoped>
.my-submission-page {
  height: 100%;
  
  .art-table-card {
    margin-top: 16px;
  }
  
  .statistics-info {
    display: flex;
    gap: 24px;
    
    :deep(.el-statistic) {
      text-align: center;
      
      .el-statistic__head {
        font-size: 12px;
        color: var(--el-text-color-secondary);
      }
      
      .el-statistic__content {
        font-size: 16px;
        font-weight: 600;
      }
    }
  }
  
  .submission-detail {
    .detail-section {
      margin-top: 20px;
      
      h4 {
        margin-bottom: 12px;
        font-size: 14px;
        color: var(--el-text-color-regular);
      }
      
      .tag-item {
        margin-right: 8px;
        margin-bottom: 4px;
      }
      
      .file-list {
        .file-item {
          display: flex;
          align-items: center;
          padding: 8px 0;
          border-bottom: 1px solid var(--el-border-color-lighter);
          
          &:last-child {
            border-bottom: none;
          }
          
          .file-icon {
            margin-right: 8px;
            font-size: 16px;
            color: var(--el-text-color-secondary);
          }
          
          .file-name {
            flex: 1;
            margin-right: 12px;
          }
          
          .file-size {
            margin-right: 12px;
            font-size: 12px;
            color: var(--el-text-color-secondary);
          }
        }
      }
      
      .log-item {
        .log-action {
          font-weight: 600;
          margin-bottom: 4px;
        }
        
        .log-status {
          color: var(--el-color-primary);
          margin-bottom: 4px;
        }
        
        .log-comment {
          color: var(--el-text-color-regular);
          margin-bottom: 4px;
        }
        
        .log-user {
          font-size: 12px;
          color: var(--el-text-color-secondary);
        }
      }
    }
  }
}
</style>
