<template>
  <div class="create-submission-page">
    <ElCard shadow="never">
      <template #header>
        <div class="card-header">
          <h3>{{ isEdit ? '编辑投稿' : '新建投稿' }}</h3>
          <div class="header-actions">
            <ElButton @click="saveDraft" :loading="draftLoading">保存草稿</ElButton>
            <ElButton type="primary" @click="submitSubmission" :loading="submitLoading">
              提交投稿
            </ElButton>
          </div>
        </div>
      </template>

      <ElForm ref="formRef" :model="formData" :rules="rules" label-width="120px">
        <!-- 基本信息 -->
        <ElRow :gutter="20">
          <ElCol :span="18">
            <ElFormItem label="投稿标题" prop="title">
              <ElInput
                v-model="formData.title"
                placeholder="请输入投稿标题（最多100个字符）"
                maxlength="100"
                show-word-limit
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="6">
            <ElFormItem label="投稿类型" prop="type">
              <ElSelect v-model="formData.type" placeholder="请选择投稿类型">
                <ElOption label="图文文章" value="article" />
                <ElOption label="视频" value="video" />
                <ElOption label="混合媒体" value="mixed" />
              </ElSelect>
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElFormItem label="投稿描述" prop="description">
          <ElInput
            v-model="formData.description"
            type="textarea"
            :rows="4"
            placeholder="请输入投稿描述（最多500个字符）"
            maxlength="500"
            show-word-limit
          />
        </ElFormItem>

        <ElRow :gutter="20">
          <ElCol :span="12">
            <ElFormItem label="标签">
              <ElInput
                v-model="formData.tags"
                placeholder="多个标签用逗号分隔，如：科技,创新,AI"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="关键词">
              <ElInput
                v-model="formData.keywords"
                placeholder="多个关键词用逗号分隔"
              />
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElFormItem label="分类">
          <ElCheckboxGroup v-model="formData.categoryIds">
            <ElCheckbox
              v-for="category in categories"
              :key="category.id"
              :label="category.id"
            >
              {{ category.name }}
            </ElCheckbox>
          </ElCheckboxGroup>
        </ElFormItem>

        <!-- 内容编辑 -->
        <ElFormItem label="投稿内容" prop="content">
          <ArtWangEditor
            ref="editorRef"
            v-model="formData.content"
            height="400px"
            placeholder="请输入投稿内容..."
          />
        </ElFormItem>

        <!-- 文件上传 -->
        <ElFormItem label="附件文件">
          <div class="file-upload-section">
            <ElUpload
              ref="uploadRef"
              :action="uploadUrl"
              :headers="uploadHeaders"
              :data="{ submission_id: submissionId }"
              :before-upload="beforeUpload"
              :on-success="handleUploadSuccess"
              :on-error="handleUploadError"
              :on-remove="handleRemoveFile"
              :file-list="fileList"
              multiple
              drag
            >
              <ElIcon class="el-icon--upload"><UploadFilled /></ElIcon>
              <div class="el-upload__text">
                将文件拖到此处，或<em>点击上传</em>
              </div>
              <template #tip>
                <div class="el-upload__tip">
                  支持图片、视频、文档等格式，单个文件不超过50MB
                </div>
              </template>
            </ElUpload>

            <!-- 文件列表 -->
            <div v-if="uploadedFiles.length" class="uploaded-files">
              <h4>已上传文件</h4>
              <div class="file-grid">
                <div
                  v-for="file in uploadedFiles"
                  :key="file.id"
                  class="file-item"
                  :class="{ 'is-image': file.file_category === 'image' }"
                >
                  <div class="file-preview">
                    <img
                      v-if="file.file_category === 'image'"
                      :src="getFilePreviewUrl(file)"
                      :alt="file.original_name"
                    />
                    <ElIcon v-else class="file-icon">
                      <Document v-if="file.file_category === 'document'" />
                      <VideoPlay v-else-if="file.file_category === 'video'" />
                      <Files v-else />
                    </ElIcon>
                  </div>
                  <div class="file-info">
                    <div class="file-name" :title="file.original_name">
                      {{ file.original_name }}
                    </div>
                    <div class="file-size">{{ formatFileSize(file.file_size) }}</div>
                  </div>
                  <div class="file-actions">
                    <ElButton size="small" @click="previewFile(file)">预览</ElButton>
                    <ElButton size="small" type="danger" @click="deleteFile(file)">
                      删除
                    </ElButton>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </ElFormItem>
      </ElForm>
    </ElCard>

    <!-- 文件预览对话框 -->
    <ElDialog v-model="previewVisible" title="文件预览" width="80%" align-center>
      <div class="file-preview-content">
        <img
          v-if="previewFile && previewFile.file_category === 'image'"
          :src="getFilePreviewUrl(previewFile)"
          style="max-width: 100%; max-height: 500px;"
        />
        <video
          v-else-if="previewFile && previewFile.file_category === 'video'"
          :src="getFilePreviewUrl(previewFile)"
          controls
          style="max-width: 100%; max-height: 500px;"
        />
        <div v-else class="document-preview">
          <ElIcon class="document-icon"><Document /></ElIcon>
          <p>{{ previewFile?.original_name }}</p>
          <ElButton @click="downloadFile(previewFile)">下载文件</ElButton>
        </div>
      </div>
    </ElDialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox, FormInstance } from 'element-plus'
import { UploadFilled, Document, VideoPlay, Files } from '@element-plus/icons-vue'
import type { FormRules, UploadFile, UploadFiles } from 'element-plus'
import { useUserStore } from '@/store/modules/user'
import {
  createSubmission,
  updateSubmission,
  getSubmissionDetail,
  submitSubmission as submitSubmissionApi,
  getAllActiveCategories,
  uploadFile,
  deleteFile as deleteFileApi,
  downloadFile as downloadFileApi
} from '@/api/submission/api'

defineOptions({ name: 'CreateSubmission' })

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const formRef = ref<FormInstance>()
const editorRef = ref()
const uploadRef = ref()
const draftLoading = ref(false)
const submitLoading = ref(false)
const previewVisible = ref(false)
const previewFile = ref<any>(null)
const categories = ref([])
const fileList = ref<UploadFiles>([])
const uploadedFiles = ref([])

// 判断是否为编辑模式
const isEdit = computed(() => !!route.query.id)
const submissionId = ref<number | null>(null)

// 表单数据
const formData = reactive({
  title: '',
  type: 'article',
  description: '',
  content: '',
  tags: '',
  keywords: '',
  categoryIds: [] as number[]
})

// 表单验证规则
const rules: FormRules = {
  title: [
    { required: true, message: '请输入投稿标题', trigger: 'blur' },
    { min: 2, max: 100, message: '标题长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择投稿类型', trigger: 'change' }
  ],
  description: [
    { max: 500, message: '描述不能超过 500 个字符', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入投稿内容', trigger: 'blur' }
  ]
}

// 上传配置
const uploadUrl = computed(() => `${import.meta.env.VITE_API_URL}/api/v1/admin/submission/file/upload`)
const uploadHeaders = computed(() => ({
  Authorization: `Bearer ${userStore.accessToken}`
}))

// 方法
const loadCategories = async () => {
  try {
    const response = await getAllActiveCategories()
    categories.value = response.data
  } catch (error) {
    console.error('加载分类失败:', error)
  }
}

const loadSubmissionDetail = async () => {
  if (!route.query.id) return
  
  try {
    const response = await getSubmissionDetail(Number(route.query.id))
    const submission = response.data
    
    Object.assign(formData, {
      title: submission.title,
      type: submission.type,
      description: submission.description,
      content: submission.content || '',
      tags: submission.tags,
      keywords: submission.keywords,
      categoryIds: submission.categories?.map((cat: any) => cat.id) || []
    })
    
    submissionId.value = submission.id
    uploadedFiles.value = submission.files || []
  } catch (error) {
    console.error('加载投稿详情失败:', error)
    ElMessage.error('加载投稿详情失败')
  }
}

const saveDraft = async () => {
  try {
    draftLoading.value = true
    
    const data = {
      ...formData,
      category_ids: formData.categoryIds
    }
    
    if (isEdit.value && submissionId.value) {
      await updateSubmission({ id: submissionId.value, ...data })
      ElMessage.success('草稿保存成功')
    } else {
      const response = await createSubmission(data)
      submissionId.value = response.data.id
      ElMessage.success('草稿创建成功')
    }
  } catch (error) {
    console.error('保存草稿失败:', error)
    ElMessage.error('保存草稿失败')
  } finally {
    draftLoading.value = false
  }
}

const submitSubmission = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    // 先保存草稿
    await saveDraft()
    
    if (!submissionId.value) {
      ElMessage.error('投稿ID不存在')
      return
    }
    
    submitLoading.value = true
    
    // 提交投稿
    await submitSubmissionApi(submissionId.value)
    ElMessage.success('投稿提交成功')
    
    // 跳转到投稿列表
    router.push('/my-submission/list')
  } catch (error) {
    console.error('提交投稿失败:', error)
    ElMessage.error('提交投稿失败')
  } finally {
    submitLoading.value = false
  }
}

const beforeUpload = (file: UploadFile) => {
  if (!submissionId.value) {
    ElMessage.error('请先保存草稿再上传文件')
    return false
  }
  
  const isValidType = /\.(jpg|jpeg|png|gif|bmp|mp4|avi|mov|wmv|flv|pdf|doc|docx|xls|xlsx|ppt|pptx|txt|zip|rar)$/i.test(file.name)
  if (!isValidType) {
    ElMessage.error('不支持的文件类型')
    return false
  }
  
  const isValidSize = file.size! / 1024 / 1024 < 50
  if (!isValidSize) {
    ElMessage.error('文件大小不能超过50MB')
    return false
  }
  
  return true
}

const handleUploadSuccess = (response: any, file: UploadFile) => {
  if (response.code === 200) {
    uploadedFiles.value.push(response.data)
    ElMessage.success('文件上传成功')
  } else {
    ElMessage.error(response.message || '文件上传失败')
  }
}

const handleUploadError = (error: any) => {
  console.error('文件上传失败:', error)
  ElMessage.error('文件上传失败')
}

const handleRemoveFile = (file: UploadFile) => {
  // 这里处理从上传列表中移除文件的逻辑
}

const deleteFile = async (file: any) => {
  try {
    await ElMessageBox.confirm('确定要删除这个文件吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await deleteFileApi(file.id)
    uploadedFiles.value = uploadedFiles.value.filter(f => f.id !== file.id)
    ElMessage.success('文件删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除文件失败:', error)
      ElMessage.error('删除文件失败')
    }
  }
}

const previewFile = (file: any) => {
  previewFile.value = file
  previewVisible.value = true
}

const downloadFile = async (file: any) => {
  try {
    const response = await downloadFileApi(file.id)
    const url = window.URL.createObjectURL(new Blob([response]))
    const link = document.createElement('a')
    link.href = url
    link.download = file.original_name
    link.click()
    window.URL.revokeObjectURL(url)
  } catch (error) {
    console.error('下载文件失败:', error)
    ElMessage.error('下载文件失败')
  }
}

const getFilePreviewUrl = (file: any) => {
  return `${import.meta.env.VITE_API_URL}/api/v1/admin/submission/file/download?id=${file.id}`
}

const formatFileSize = (size: number) => {
  if (size < 1024) return size + ' B'
  if (size < 1024 * 1024) return (size / 1024).toFixed(1) + ' KB'
  return (size / 1024 / 1024).toFixed(1) + ' MB'
}

// 生命周期
onMounted(() => {
  loadCategories()
  if (isEdit.value) {
    loadSubmissionDetail()
  }
})
</script>

<style lang="scss" scoped>
.create-submission-page {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    h3 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
    }
  }
  
  .file-upload-section {
    .uploaded-files {
      margin-top: 20px;
      
      h4 {
        margin-bottom: 12px;
        font-size: 14px;
        color: var(--el-text-color-regular);
      }
      
      .file-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 12px;
        
        .file-item {
          border: 1px solid var(--el-border-color);
          border-radius: 6px;
          padding: 12px;
          
          .file-preview {
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 8px;
            background: var(--el-fill-color-light);
            border-radius: 4px;
            
            img {
              max-width: 100%;
              max-height: 100%;
              object-fit: cover;
            }
            
            .file-icon {
              font-size: 32px;
              color: var(--el-text-color-placeholder);
            }
          }
          
          .file-info {
            margin-bottom: 8px;
            
            .file-name {
              font-size: 12px;
              color: var(--el-text-color-primary);
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
            
            .file-size {
              font-size: 11px;
              color: var(--el-text-color-secondary);
            }
          }
          
          .file-actions {
            display: flex;
            gap: 4px;
            
            .el-button {
              flex: 1;
              font-size: 11px;
              padding: 4px 8px;
            }
          }
        }
      }
    }
  }
  
  .file-preview-content {
    text-align: center;
    
    .document-preview {
      .document-icon {
        font-size: 64px;
        color: var(--el-text-color-placeholder);
        margin-bottom: 16px;
      }
      
      p {
        margin-bottom: 16px;
        font-size: 16px;
      }
    }
  }
}
</style>
