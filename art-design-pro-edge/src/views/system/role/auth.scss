.auth-drawer-namespace {
  .drawer-content {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 12px;
  }
  
  .drawer-header {
    margin-bottom: 16px;
  }
  
  .menu-tree-container {
    flex: 1;
    overflow-y: auto;
    padding: 10px 0;
    border-radius: 4px;
    background-color: var(--el-fill-color-light);
  }
  
  // 树组件样式
  :deep(.el-tree) {
    background: transparent;
    
    // 强制元素在单元格里
    .el-tree-node__content {
      height: auto !important;
      min-height: 32px;
      padding: 4px 0;
      display: block !important; // 强制为块级元素
      width: 100%;
    }
    
    .el-tree-node__label {
      width: 100%;
    }
    
    .el-tree-node.is-current > .el-tree-node__content {
      background-color: var(--el-color-primary-light-9);
    }
    
    // 禁用树节点的默认Flex布局
    .el-tree-node__content {
      overflow: visible;
      height: auto !important;
      display: block !important;
    }
  }
  
  .drawer-footer {
    padding: 16px 0 0;
    text-align: right;
    border-top: 1px solid var(--el-border-color-light);
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
  
  .loading-container {
    padding: 20px;
  }
  
  // 菜单树项目 - 使用表格布局模式
  .menu-tree-item {
    width: 100%;
    display: table;
    table-layout: fixed;
    margin: 4px 0;
  }
  
  // 菜单行
  .menu-row {
    display: table-row;
    width: 100%;
  }
  
  // 权限标签行
  .auth-row {
    display: table-row;
    width: 100%;
  }
  
  // 菜单名称区域
  .menu-name {
    display: table-cell;
    font-weight: 600;
    color: var(--el-text-color-primary);
    min-height: 24px;
    vertical-align: middle;
    padding: 4px 0;
    
    .menu-icon {
      margin-right: 6px;
      font-size: 16px;
      vertical-align: middle;
    }
    
    .menu-title {
      font-size: 14px;
      color: var(--el-text-color-primary);
      font-weight: 600;
      line-height: 1.4;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      vertical-align: middle;
    }
  }
  
  // 权限标签容器
  .auth-tags-wrapper {
    display: table-cell;
    padding-left: 24px;
    padding-top: 4px;
    padding-bottom: 4px;
    border-top: 1px dashed #e0e0e0;
  }
  
  // 权限标签样式
  .auth-tag {
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 12px;
    margin-right: 6px;
    margin-bottom: 4px;
    display: inline-block;
    
    &:hover {
      transform: scale(1.05);
    }
    
    &:active {
      transform: scale(0.98);
    }
  }
  
  // 确保树节点有足够的高度
  :deep(.el-tree-node) {
    .el-tree-node__children {
      overflow: visible;
    }
  }
  
  // 菜单树节点容器
  .menu-tree-node {
    width: 100%;
    display: flex;
    flex-direction: column;
    margin-bottom: 2px;
    align-items: center;
    
    &.auth-node {
      padding-left: 4px;
    }
    
    .menu-name-row {
      display: flex;
      align-items: center;
      width: 100%;
      padding: 4px 0;
      
      .menu-icon {
        margin-right: 8px;
        font-size: 16px;
      }
      
      .menu-title {
        font-weight: 500;
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    
    .auth-name-row {
      display: flex;
      align-items: center;
      padding: 3px 0;
      
      .auth-title {
        font-size: 13px;
        color: #606266;
        position: relative;
        padding-left: 16px;
        
        &:before {
          content: "•";
          position: absolute;
          left: 4px;
          font-size: 16px;
          color: #909399;
        }
      }
    }
  }

  // 权限标签行
  .auth-tags-row {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    padding-left: 24px;
    padding-top: 6px;
    padding-bottom: 2px;
    border-top: 1px dashed #e0e0e0;
    margin-top: 6px;
  }

  // 响应式调整
  @media (max-width: 768px) {
    .menu-tree-container {
      padding: 0;
    }
    
    .auth-tags-wrapper {
      padding-left: 16px;
    }

    .auth-tags-row {
      padding-left: 16px;
    }
  }

  // 树节点样式优化
  .el-tree-node__content {
    height: auto !important;
    padding: 4px 0;
  }

  // 调整权限节点的样式
  .el-tree .el-tree-node.is-expanded > .el-tree-node__children {
    .auth-node {
      background-color: #f5f7fa;
      border-radius: 4px;
      margin-left: 24px;
      margin-top: 2px;
      
      &:hover {
        background-color: #ebeef5;
      }
    }
  }
  
  // 父级菜单节点样式
  .el-tree-node:has(.el-tree-node__children) > .el-tree-node__content {
    font-weight: 600;
  }
  
  // 对树节点的样式调整
  .el-tree {
    .el-tree-node {
      .el-tree-node__content {
        padding: 2px 0;
      }
    }
  }
}
