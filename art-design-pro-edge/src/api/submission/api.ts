import api from '@/api/client'
import { ApiResponse } from '@/api/client'

// 投稿管理相关API

// 获取投稿列表
export const getSubmissionList = (params: {
  title?: string
  status?: string
  type?: string
  user_id?: number
  page?: number
  page_size?: number
}): Promise<ApiResponse<any>> => {
  return api.get({ url: '/submission/list', params })
}

// 获取投稿详情
export const getSubmissionDetail = (id: number): Promise<ApiResponse<any>> => {
  return api.get({ url: '/submission/detail', params: { id } })
}

// 创建投稿
export const createSubmission = (data: {
  title: string
  description?: string
  type: string
  tags?: string
  keywords?: string
  category_ids?: number[]
}): Promise<ApiResponse<any>> => {
  return api.post({ url: '/submission/create', data })
}

// 更新投稿
export const updateSubmission = (data: {
  id: number
  title: string
  description?: string
  type: string
  tags?: string
  keywords?: string
  category_ids?: number[]
}): Promise<ApiResponse<any>> => {
  return api.put({ url: '/submission/update', data })
}

// 删除投稿
export const deleteSubmission = (id: number): Promise<ApiResponse<any>> => {
  return api.del({ url: '/submission/delete', params: { id } })
}

// 提交投稿
export const submitSubmission = (id: number): Promise<ApiResponse<any>> => {
  return api.put({ url: '/submission/submit', data: { id } })
}

// 获取我的投稿列表
export const getMySubmissions = (params: {
  status?: string
  type?: string
  page?: number
  page_size?: number
}): Promise<ApiResponse<any>> => {
  return api.get({ url: '/submission/my', params })
}

// 获取投稿统计
export const getSubmissionStatistics = (user_id?: number): Promise<ApiResponse<any>> => {
  return api.get({ url: '/submission/statistics', params: user_id ? { user_id } : {} })
}

// 状态管理相关API

// 更新投稿状态
export const updateSubmissionStatus = (data: {
  id: number
  status: string
  comment?: string
  admin_note?: string
}): Promise<ApiResponse<any>> => {
  return api.put({ url: '/submission/status', data })
}

// 安排投稿发布
export const scheduleSubmission = (data: {
  id: number
  publish_date: string
  comment?: string
}): Promise<ApiResponse<any>> => {
  return api.put({ url: '/submission/schedule', data })
}

// 发布投稿
export const publishSubmission = (data: {
  id: number
  comment?: string
}): Promise<ApiResponse<any>> => {
  return api.put({ url: '/submission/publish', data })
}

// 撤回投稿
export const withdrawSubmission = (data: {
  id: number
  comment?: string
}): Promise<ApiResponse<any>> => {
  return api.put({ url: '/submission/withdraw', data })
}

// 获取投稿操作日志
export const getSubmissionLogs = (id: number): Promise<ApiResponse<any>> => {
  return api.get({ url: '/submission/logs', params: { id } })
}

// 批量更新状态
export const batchUpdateStatus = (data: {
  ids: number[]
  status: string
  comment?: string
}): Promise<ApiResponse<any>> => {
  return api.put({ url: '/submission/batch-status', data })
}

// 文件管理相关API

// 上传文件
export const uploadFile = (formData: FormData): Promise<ApiResponse<any>> => {
  return api.post({ 
    url: '/submission/file/upload', 
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 获取文件列表
export const getFileList = (params: {
  submission_id?: number
  file_type?: string
  file_category?: string
  page?: number
  page_size?: number
}): Promise<ApiResponse<any>> => {
  return api.get({ url: '/submission/file/list', params })
}

// 删除文件
export const deleteFile = (id: number): Promise<ApiResponse<any>> => {
  return api.del({ url: '/submission/file/delete', params: { id } })
}

// 下载文件
export const downloadFile = (id: number): Promise<any> => {
  return api.get({ 
    url: '/submission/file/download', 
    params: { id },
    responseType: 'blob'
  })
}

// 更新文件排序
export const updateFileSortOrder = (data: {
  file_orders: Array<{
    id: number
    sort_order: number
  }>
}): Promise<ApiResponse<any>> => {
  return api.put({ url: '/submission/file/sort', data })
}

// 获取文件统计
export const getFileStatistics = (): Promise<ApiResponse<any>> => {
  return api.get({ url: '/submission/file/statistics' })
}

// 分类管理相关API

// 获取分类列表
export const getCategoryList = (params?: {
  name?: string
  is_active?: boolean
  page?: number
  page_size?: number
}): Promise<ApiResponse<any>> => {
  return api.get({ url: '/submission/category/list', params })
}

// 获取所有启用的分类
export const getAllActiveCategories = (): Promise<ApiResponse<any>> => {
  return api.get({ url: '/submission/category/active' })
}

// 创建分类
export const createCategory = (data: {
  name: string
  description?: string
  sort_order?: number
}): Promise<ApiResponse<any>> => {
  return api.post({ url: '/submission/category/create', data })
}

// 更新分类
export const updateCategory = (data: {
  id: number
  name: string
  description?: string
  sort_order?: number
  is_active?: boolean
}): Promise<ApiResponse<any>> => {
  return api.put({ url: '/submission/category/update', data })
}

// 删除分类
export const deleteCategory = (id: number): Promise<ApiResponse<any>> => {
  return api.del({ url: '/submission/category/delete', params: { id } })
}

// 发布日历相关API

// 获取发布日历
export const getPublishCalendar = (params: {
  start_date: string
  end_date: string
}): Promise<ApiResponse<any>> => {
  return api.get({ url: '/submission/calendar', params })
}

// 更新发布日历
export const updatePublishCalendar = (data: {
  date: string
  max_submissions?: number
  notes?: string
  is_holiday?: boolean
}): Promise<ApiResponse<any>> => {
  return api.put({ url: '/submission/calendar/update', data })
}

// 获取可用发布日期
export const getAvailablePublishDates = (params: {
  start_date: string
  end_date: string
}): Promise<ApiResponse<any>> => {
  return api.get({ url: '/submission/calendar/available', params })
}

// 获取日历统计
export const getCalendarStatistics = (params: {
  start_date: string
  end_date: string
}): Promise<ApiResponse<any>> => {
  return api.get({ url: '/submission/calendar/statistics', params })
}

// 设置节假日
export const setHolidayDates = (data: {
  dates: string[]
  is_holiday: boolean
}): Promise<ApiResponse<any>> => {
  return api.put({ url: '/submission/calendar/holiday', data })
}

// 投稿状态枚举
export const SUBMISSION_STATUS = {
  DRAFT: 'draft',
  SUBMITTED: 'submitted',
  REVIEWING: 'reviewing',
  APPROVED: 'approved',
  REJECTED: 'rejected',
  REVISION: 'revision',
  SCHEDULED: 'scheduled',
  PUBLISHED: 'published',
  WITHDRAWN: 'withdrawn'
} as const

// 投稿类型枚举
export const SUBMISSION_TYPE = {
  ARTICLE: 'article',
  VIDEO: 'video',
  MIXED: 'mixed'
} as const

// 投稿优先级枚举
export const SUBMISSION_PRIORITY = {
  LOW: 'low',
  NORMAL: 'normal',
  HIGH: 'high',
  URGENT: 'urgent'
} as const

// 文件分类枚举
export const FILE_CATEGORY = {
  IMAGE: 'image',
  VIDEO: 'video',
  DOCUMENT: 'document'
} as const
