import { RoutesAlias } from '../routesAlias'
import { AppRouteRecord } from '@/types/router'

/**
 * 菜单列表、异步路由
 *
 * 支持两种模式:
 * 1. 前端静态配置 - 直接使用本文件中定义的路由配置
 * 2. 后端动态配置 - 后端返回菜单数据，前端解析生成路由
 *
 * 菜单标题（title）:
 * 可以是 i18n 的 key，也可以是字符串，比如：'用户列表'
 */
export const asyncRoutes: AppRouteRecord[] = [
  {
    id: 1,
    name: 'Dashboard',
    path: '/dashboard',
    component: RoutesAlias.Home,
    meta: {
      title: '仪表盘',
      icon: '&#xe721;',
      keepAlive: false,
      roles: ['R_SUPER', 'R_ADMIN']
    },
    children: [
      {
        id: 101,
        path: 'console',
        name: 'Console',
        component: RoutesAlias.Dashboard,
        meta: {
          title: '工作台',
          keepAlive: false,
          roles: ['R_SUPER', 'R_ADMIN']
        }
      },
      {
        id: 102,
        path: 'analysis',
        name: 'Analysis',
        component: RoutesAlias.Analysis,
        meta: {
          title: '分析页',
          keepAlive: false,
          roles: ['R_SUPER', 'R_ADMIN']
        }
      }
    ]
  },
  // 投稿管理模块
  {
    id: 10,
    name: 'Submission',
    path: '/submission',
    component: RoutesAlias.Home,
    meta: {
      title: '投稿管理',
      icon: '&#xe6b2;',
      keepAlive: false,
      roles: ['R_SUPER', 'R_ADMIN']
    },
    children: [
      {
        id: 101,
        path: 'list',
        name: 'SubmissionList',
        component: () => import('@/views/submission/list/index.vue'),
        meta: {
          title: '投稿列表',
          keepAlive: true,
          roles: ['R_SUPER', 'R_ADMIN']
        }
      },
      {
        id: 102,
        path: 'calendar',
        name: 'SubmissionCalendar',
        component: () => import('@/views/submission/calendar/index.vue'),
        meta: {
          title: '发布日历',
          keepAlive: true,
          roles: ['R_SUPER', 'R_ADMIN']
        }
      },
      {
        id: 103,
        path: 'category',
        name: 'SubmissionCategory',
        component: () => import('@/views/submission/category/index.vue'),
        meta: {
          title: '分类管理',
          keepAlive: true,
          roles: ['R_SUPER', 'R_ADMIN']
        }
      },
      {
        id: 104,
        path: 'statistics',
        name: 'SubmissionStatistics',
        component: () => import('@/views/submission/statistics/index.vue'),
        meta: {
          title: '统计分析',
          keepAlive: true,
          roles: ['R_SUPER', 'R_ADMIN']
        }
      }
    ]
  },
  // 我的投稿模块（用户端）
  {
    id: 20,
    name: 'MySubmission',
    path: '/my-submission',
    component: RoutesAlias.Home,
    meta: {
      title: '我的投稿',
      icon: '&#xe6b1;',
      keepAlive: false,
      roles: ['R_SUPER', 'R_ADMIN', 'R_USER']
    },
    children: [
      {
        id: 201,
        path: 'list',
        name: 'MySubmissionList',
        component: () => import('@/views/my-submission/list/index.vue'),
        meta: {
          title: '投稿列表',
          keepAlive: true,
          roles: ['R_SUPER', 'R_ADMIN', 'R_USER']
        }
      },
      {
        id: 202,
        path: 'create',
        name: 'CreateSubmission',
        component: () => import('@/views/my-submission/create/index.vue'),
        meta: {
          title: '新建投稿',
          keepAlive: false,
          roles: ['R_SUPER', 'R_ADMIN', 'R_USER']
        }
      },
      {
        id: 203,
        path: 'calendar',
        name: 'ViewCalendar',
        component: () => import('@/views/my-submission/calendar/index.vue'),
        meta: {
          title: '发布日历',
          keepAlive: true,
          roles: ['R_SUPER', 'R_ADMIN', 'R_USER']
        }
      }
    ]
  }
]
