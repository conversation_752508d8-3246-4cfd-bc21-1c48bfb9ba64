This file is a merged representation of the entire codebase, combined into a single document by Repomix.
The content has been processed where security check has been disabled.

# File Summary

## Purpose
This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.

## File Format
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Repository files (if enabled)
5. Multiple file entries, each consisting of:
  a. A header with the file path (## File: path/to/file)
  b. The full contents of the file in a code block

## Usage Guidelines
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.

## Notes
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Security check has been disabled - content may contain sensitive information
- Files are sorted by Git change count (files with more changes are at the bottom)

# Directory Structure
```
art-design-pro-edge-go-server-main/
  .vscode/
    settings.json
  api/
    app/
      system/
        department/
          department.go
        menu/
          auth.go
          menu.go
        role/
          role.go
        user/
          captcha.go
          info.go
          login.go
          menu.go
          user.go
    middleware/
      cross-domain.go
      jwt.go
      page.go
      params.go
    response/
      code.go
      format.go
    router.go
  common/
    cron/
      manager.go
      user_cache.go
    menu/
      tree.go
  config/
    config.go
  db/
    pgdb/
      system/
        auth.go
        department.go
        menu.go
        migrate.go
        model.go
        role.go
        user.go
      client.go
    rdb/
      captcha/
        store.go
      systemUser/
        user.go
      client.go
  docker/
    docker-compose.yml
  util/
    authentication/
      jwt.go
    encryption/
      md5.go
    id/
      id.go
    log/
      log.go
    path-tool/
      path.go
    run-model/
      model.go
  .gitignore
  go.mod
  LICENSE
  main.go
  README.md
```

# Files

## File: art-design-pro-edge-go-server-main/.vscode/settings.json
````json
{
    "gopls": {
        "formatting.local": "api-server"  // Value from go.mod > model
    }
}
````

## File: art-design-pro-edge-go-server-main/api/app/system/department/department.go
````go
package department

import (
	"errors"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"api-server/api/middleware"
	"api-server/api/response"
	"api-server/db/pgdb/system"
)

func AddDepartment(c *gin.Context) {
	params := &struct {
		Name   string `json:"name" form:"name" binding:"required"`
		Status int    `json:"status" form:"status" binding:"required"`
		Sort   int    `json:"sort" form:"sort"`
	}{}
	if !middleware.CheckParam(params, c) {
		return
	}
	depatment := system.SystemDepartment{
		Name:   params.Name,
		Status: uint(params.Status),
		Sort:   uint(params.Sort),
	}
	err := system.AddDepartment(&depatment)
	if err != nil {
		response.ReturnError(c, response.DATA_LOSS, "添加部门失败")
		return
	}
	response.ReturnData(c, depatment)
}

func UpdateDepartment(c *gin.Context) {
	params := &struct {
		ID     uint   `json:"id" form:"id" binding:"required"`
		Name   string `json:"name" form:"name" binding:"required"`
		Status int    `json:"status" form:"status" binding:"required"`
		Sort   int    `json:"sort" form:"sort"`
	}{}
	if !middleware.CheckParam(params, c) {
		return
	}
	department := system.SystemDepartment{
		Model:  gorm.Model{ID: params.ID},
		Name:   params.Name,
		Status: uint(params.Status),
		Sort:   uint(params.Sort),
	}
	err := system.UpdateDepartment(&department)
	if err != nil {
		response.ReturnError(c, response.DATA_LOSS, "更新部门失败")
		return
	}
	response.ReturnData(c, department)
}

func GetDepartmentList(c *gin.Context) {
	params := &struct {
		Name   string `json:"name" form:"name"`
		Status uint   `json:"status" form:"status"`
	}{}
	if !middleware.CheckParam(params, c) {
		return
	}

	// 获取分页参数
	page := middleware.GetPage(c)
	pageSize := middleware.GetPageSize(c)

	department := system.SystemDepartment{
		Name:   params.Name,
		Status: params.Status,
	}

	// 调用带分页的查询函数
	departments, total, err := system.FindDepartmentList(&department, page, pageSize)
	if err != nil {
		response.ReturnError(c, response.DATA_LOSS, "查询部门失败")
		return
	}

	// 返回带总数的结果
	response.ReturnDataWithCount(c, int(total), departments)
}

func DeleteDepartment(c *gin.Context) {
	params := &struct {
		ID uint `json:"id" form:"id" binding:"required"`
	}{}
	if !middleware.CheckParam(params, c) {
		return
	}
	department := system.SystemDepartment{Model: gorm.Model{ID: params.ID}}
	err := system.GetDepartment(&department)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.ReturnError(c, response.DATA_LOSS, "部门不存在")
			return
		}
		response.ReturnError(c, response.DATA_LOSS, "查询部门失败")
		return
	}
	if len(department.SystemUsers) > 0 {
		response.ReturnError(c, response.DATA_LOSS, "请先删除部门下的用户")
		return
	}
	err = system.DeleteDepartment(&department)
	if err != nil {
		response.ReturnError(c, response.DATA_LOSS, "删除部门失败")
		return
	}
	response.ReturnData(c, department)
}
````

## File: art-design-pro-edge-go-server-main/api/app/system/menu/auth.go
````go
package menu

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"api-server/api/middleware"
	"api-server/api/response"
	"api-server/db/pgdb/system"
)

func AddMenuAuth(c *gin.Context) {
	params := &struct {
		MenuID uint   `json:"menu_id"`
		Mark   string `json:"mark"` // 标识
		Title  string `json:"title"`
	}{}
	if !middleware.CheckParam(params, c) {
		return
	}
	auth := system.SystemMenuAuth{
		MenuID: params.MenuID,
		Mark:   params.Mark,
		Title:  params.Title,
	}
	if err := system.AddMenuAuth(&auth); err != nil {
		response.ReturnError(c, response.DATA_LOSS, "添加菜单权限失败")
		return
	}
	response.ReturnData(c, auth)
}

func UpdateMenuAuth(c *gin.Context) {
	params := &struct {
		ID     uint   `json:"id"`
		Title  string `json:"title"`
		Mark   string `json:"mark"` // 标识
		MenuID uint   `json:"menu_id"`
	}{}
	if !middleware.CheckParam(params, c) {
		return
	}
	auth := system.SystemMenuAuth{
		Model:  gorm.Model{ID: params.ID},
		Title:  params.Title,
		Mark:   params.Mark,
		MenuID: params.MenuID,
	}
	if err := system.UpdateMenuAuth(&auth); err != nil {
		response.ReturnError(c, response.DATA_LOSS, "更新菜单权限失败")
		return
	}
	response.ReturnData(c, auth)
}

func DeleteMenuAuth(c *gin.Context) {
	params := &struct {
		ID uint `json:"id" form:"id" binding:"required"`
	}{}
	if !middleware.CheckParam(params, c) {
		return
	}
	auth := system.SystemMenuAuth{Model: gorm.Model{ID: params.ID}}
	if err := system.DeleteMenuAuth(&auth); err != nil {
		response.ReturnError(c, response.DATA_LOSS, "删除菜单权限失败")
		return
	}
	response.ReturnData(c, auth)
}

func GetMenuAuthList(c *gin.Context) {
	params := &struct {
		MenuID uint `json:"menu_id" form:"menu_id" binding:"required"`
	}{}
	if !middleware.CheckParam(params, c) {
		return
	}
	auth := system.SystemMenuAuth{MenuID: params.MenuID}
	auths, err := system.FindMenuAuthList(&auth)
	if err != nil {
		response.ReturnError(c, response.DATA_LOSS, "查询菜单权限失败")
		return
	}
	response.ReturnData(c, auths)
}
````

## File: art-design-pro-edge-go-server-main/api/app/system/menu/menu.go
````go
package menu

import (
	"encoding/json"
	"errors"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"api-server/api/middleware"
	"api-server/api/response"
	"api-server/common/menu"
	"api-server/db/pgdb/system"
)

func GetMenuList(c *gin.Context) {
	// 查询菜单数据
	menus, menup, err := system.GetMenuData()
	if err != nil {
		response.ReturnError(c, response.DATA_LOSS, "查询菜单失败")
		return
	}
	// 构建菜单树
	menuTree := menu.BuildMenuTree(menus, menup, true)
	response.ReturnData(c, menuTree)
}

func DeleteMenu(c *gin.Context) {
	params := &struct {
		ID uint `json:"id" form:"id" binding:"required"`
	}{}
	if !middleware.CheckParam(params, c) {
		return
	}
	menu := system.SystemMenu{Model: gorm.Model{ID: params.ID}}
	err := system.GetMenu(&menu)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.ReturnError(c, response.DATA_LOSS, "菜单不存在")
			return
		}
		response.ReturnError(c, response.DATA_LOSS, "查询菜单失败")
		return
	}
	children, _, err := system.FindMenuList(&system.SystemMenu{ParentID: menu.ID}, -1, -1)
	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			response.ReturnError(c, response.DATA_LOSS, "查询子菜单失败")
			return
		}
	}
	if len(children) > 0 {
		response.ReturnError(c, response.DATA_LOSS, "请先删除子菜单")
		return
	}
	if err := system.DeleteMenu(&menu); err != nil {
		response.ReturnError(c, response.DATA_LOSS, "删除菜单失败")
		return
	}
	response.ReturnData(c, menu)
}

func AddMenu(c *gin.Context) {
	params := &struct {
		Path          string `json:"path" form:"path" binding:"required"`
		Name          string `json:"name" form:"name" binding:"required"`
		Component     string `json:"component" form:"component"`
		Title         string `json:"title" form:"title" binding:"required"`
		Icon          string `json:"icon" form:"icon"`
		ShowBadge     uint   `json:"showBadge" form:"showBadge"`
		ShowTextBadge string `json:"showTextBadge" form:"showTextBadge"`
		IsHide        uint   `json:"isHide" form:"isHide" binding:"required"`
		IsHideTab     uint   `json:"isHideTab" form:"isHideTab" binding:"required"`
		Link          string `json:"link" form:"link"`
		IsIframe      uint   `json:"isIframe" form:"isIframe" binding:"required"`
		KeepAlive     uint   `json:"keepAlive" form:"keepAlive" binding:"required"`
		IsFirstLevel  uint   `json:"isFirstLevel" form:"isFirstLevel" binding:"required"`
		Status        uint   `json:"status" form:"status" binding:"required"`
		ParentID      uint   `json:"parentId" form:"parentId"`
		Sort          uint   `json:"sort" form:"sort"`
	}{}
	if !middleware.CheckParam(params, c) {
		return
	}
	if params.ShowBadge == 0 {
		params.ShowBadge = 2
	}
	var level uint = 1
	// 如果有父级ID, 则查询父级ID是否存在
	if params.ParentID != 0 {
		parentMenu := system.SystemMenu{
			Model: gorm.Model{ID: params.ParentID},
		}
		err := system.GetMenu(&parentMenu)
		if err != nil {
			response.ReturnError(c, response.DATA_LOSS, "父级菜单不存在")
			return
		}
		if parentMenu.Status != 1 {
			response.ReturnError(c, response.DATA_LOSS, "父级菜单已禁用")
			return
		}
		level = parentMenu.Level + 1
	}
	menu := system.SystemMenu{
		Path:          params.Path,
		Name:          params.Name,
		Component:     params.Component,
		Title:         params.Title,
		Icon:          params.Icon,
		ShowBadge:     params.ShowBadge,
		ShowTextBadge: params.ShowTextBadge,
		IsHide:        params.IsHide,
		IsHideTab:     params.IsHideTab,
		Link:          params.Link,
		IsIframe:      params.IsIframe,
		KeepAlive:     params.KeepAlive,
		IsFirstLevel:  params.IsFirstLevel,
		Status:        params.Status,
		Level:         level,
		ParentID:      params.ParentID,
		Sort:          params.Sort,
	}
	if err := system.AddMenu(&menu); err != nil {
		response.ReturnError(c, response.DATA_LOSS, "添加菜单失败")
		return
	}
	response.ReturnData(c, menu)
}

func UpdateMenu(c *gin.Context) {
	params := &struct {
		ID            uint   `json:"id" form:"id" binding:"required"`
		Path          string `json:"path" form:"path" binding:"required"`
		Name          string `json:"name" form:"name" binding:"required"`
		Component     string `json:"component" form:"component"`
		Title         string `json:"title" form:"title" binding:"required"`
		Icon          string `json:"icon" form:"icon"`
		ShowBadge     uint   `json:"showBadge" form:"showBadge"`
		ShowTextBadge string `json:"showTextBadge" form:"showTextBadge"`
		IsHide        uint   `json:"isHide" form:"isHide" binding:"required"`
		IsHideTab     uint   `json:"isHideTab" form:"isHideTab" binding:"required"`
		Link          string `json:"link" form:"link"`
		IsIframe      uint   `json:"isIframe" form:"isIframe" binding:"required"`
		KeepAlive     uint   `json:"keepAlive" form:"keepAlive" binding:"required"`
		IsFirstLevel  uint   `json:"isFirstLevel" form:"isFirstLevel" binding:"required"`
		Status        uint   `json:"status" form:"status" binding:"required"`
		ParentID      uint   `json:"parentId" form:"parentId"`
		Sort          uint   `json:"sort" form:"sort"`
	}{}
	if !middleware.CheckParam(params, c) {
		return
	}
	if params.ShowBadge == 0 {
		params.ShowBadge = 2
	}
	var level uint = 1
	// 如果有父级ID, 则查询父级ID是否存在
	if params.ParentID != 0 {
		parent := system.SystemMenu{Model: gorm.Model{ID: params.ParentID}}
		err := system.GetMenu(&parent)
		if err != nil {
			response.ReturnError(c, response.DATA_LOSS, "父级菜单不存在")
			return
		}
		if parent.Status != 1 {
			response.ReturnError(c, response.DATA_LOSS, "父级菜单已禁用")
			return
		}
		level = parent.Level + 1
	}
	if params.Status == 2 {
		// 判断子菜单是否是禁用状态
		children, _, err := system.FindMenuList(&system.SystemMenu{ParentID: params.ID}, -1, -1)
		if err != nil {
			if !errors.Is(err, gorm.ErrRecordNotFound) {
				response.ReturnError(c, response.DATA_LOSS, "查询子菜单失败")
				return
			}
		}
		if len(children) > 0 {
			for _, v := range children {
				if v.Status == 1 {
					response.ReturnError(c, response.DATA_LOSS, "请先禁用子菜单")
					return
				}
			}
		}
	}
	menu := system.SystemMenu{
		Model:         gorm.Model{ID: params.ID},
		Path:          params.Path,
		Name:          params.Name,
		Component:     params.Component,
		Title:         params.Title,
		Icon:          params.Icon,
		ShowBadge:     params.ShowBadge,
		ShowTextBadge: params.ShowTextBadge,
		IsHide:        params.IsHide,
		IsHideTab:     params.IsHideTab,
		Link:          params.Link,
		IsIframe:      params.IsIframe,
		KeepAlive:     params.KeepAlive,
		IsFirstLevel:  params.IsFirstLevel,
		Status:        params.Status,
		Level:         level,
		ParentID:      params.ParentID,
		Sort:          params.Sort,
	}
	if err := system.UpdateMenu(&menu); err != nil {
		response.ReturnError(c, response.DATA_LOSS, "更新菜单失败")
		return
	}
	response.ReturnData(c, menu)
}

// GetMenuListByRoleID 根据角色ID获取菜单列表
func GetMenuListByRoleID(c *gin.Context) {
	params := &struct {
		RoleID uint `json:"role_id" form:"role_id" binding:"required"`
	}{}
	if !middleware.CheckParam(params, c) {
		return
	}
	// 查询菜单数据
	allMenus, allAuths, roleMenuIds, roleAuthIds, err := system.GetMenuDataByRoleID(params.RoleID)
	if err != nil {
		response.ReturnError(c, response.DATA_LOSS, "查询角色菜单失败")
		return
	}
	// 构建带权限标记的菜单树
	menuTree := menu.BuildMenuTreeWithPermission(allMenus, allAuths, roleMenuIds, roleAuthIds, true)
	response.ReturnData(c, menuTree)
}

func UpdateMenuListByRoleID(c *gin.Context) {
	params := &struct {
		RoleID   uint   `json:"role_id" form:"role_id" binding:"required"`
		MenuData string `json:"menu_data" form:"menu_data" binding:"required"`
	}{}
	if !middleware.CheckParam(params, c) {
		return
	}
	// 尝试将 params.MenuData 转成结构体
	var menuData []menu.MenuResponse
	err := json.Unmarshal([]byte(params.MenuData), &menuData)
	if err != nil {
		response.ReturnError(c, response.DATA_LOSS, "参数错误")
		return
	}

	// 保存角色菜单数据
	err = menu.SaveRoleMenu(params.RoleID, menuData)
	if err != nil {
		response.ReturnError(c, response.DATA_LOSS, "保存角色菜单失败")
		return
	}

	response.ReturnData(c, nil)
}
````

## File: art-design-pro-edge-go-server-main/api/app/system/role/role.go
````go
package role

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"api-server/api/middleware"
	"api-server/api/response"
	"api-server/db/pgdb/system"
)

func GetRoleList(c *gin.Context) {
	params := &struct {
		Name   string `json:"name" form:"name"`
		Status uint   `json:"status" form:"status"`
	}{}
	if !middleware.CheckParam(params, c) {
		return
	}

	// 获取分页参数
	page := middleware.GetPage(c)
	pageSize := middleware.GetPageSize(c)

	role := system.SystemRole{
		Name:   params.Name,
		Status: params.Status,
	}

	// 调用带分页的查询函数
	roles, total, err := system.FindRoleList(&role, page, pageSize)
	if err != nil {
		response.ReturnError(c, response.DATA_LOSS, "获取角色列表失败")
		return
	}

	// 返回带总数的结果
	response.ReturnDataWithCount(c, int(total), roles)
}

func AddRole(c *gin.Context) {
	params := &struct {
		Name   string `json:"name" form:"name" binding:"required"`
		Status int    `json:"status" form:"status" binding:"required"`
		Desc   string `json:"desc" form:"desc"`
	}{}
	if !middleware.CheckParam(params, c) {
		return
	}
	role := system.SystemRole{
		Name:   params.Name,
		Status: uint(params.Status),
		Desc:   params.Desc,
	}
	err := system.AddRole(&role)
	if err != nil {
		response.ReturnError(c, response.DATA_LOSS, "添加角色失败")
		return
	}
	response.ReturnData(c, role)
}

func UpdateRole(c *gin.Context) {
	params := &struct {
		ID     uint   `json:"id" form:"id" binding:"required"`
		Name   string `json:"name" form:"name" binding:"required"`
		Status int    `json:"status" form:"status" binding:"required"`
		Desc   string `json:"desc" form:"desc"`
	}{}
	if !middleware.CheckParam(params, c) {
		return
	}
	role := system.SystemRole{
		Model:  gorm.Model{ID: params.ID},
		Name:   params.Name,
		Status: uint(params.Status),
		Desc:   params.Desc,
	}
	err := system.UpdateRole(&role)
	if err != nil {
		response.ReturnError(c, response.DATA_LOSS, "更新角色失败")
		return
	}
	response.ReturnData(c, role)
}

func DeleteRole(c *gin.Context) {
	params := &struct {
		ID uint `json:"id" form:"id" binding:"required"`
	}{}
	if !middleware.CheckParam(params, c) {
		return
	}
	role := system.SystemRole{
		Model: gorm.Model{ID: params.ID},
	}
	err := system.DeleteRole(&role)
	if err != nil {
		response.ReturnError(c, response.DATA_LOSS, "删除角色失败")
		return
	}
	response.ReturnData(c, role)
}
````

## File: art-design-pro-edge-go-server-main/api/app/system/user/captcha.go
````go
package user

import (
	"github.com/gin-gonic/gin"
	"github.com/mojocn/base64Captcha"
	"go.uber.org/zap"

	"api-server/api/middleware"
	"api-server/api/response"
	"api-server/db/rdb/captcha"
)

func GetCaptcha(c *gin.Context) {
	params := &struct {
		Width  int `json:"width" form:"width" binding:"required"`
		Height int `json:"height" form:"height" binding:"required"`
	}{}
	if !middleware.CheckParam(params, c) {
		return
	}
	driver := base64Captcha.NewDriverDigit(params.Height, params.Width, 6, 0.2, 50)
	client := base64Captcha.NewCaptcha(driver, captcha.GetRedisStore())
	id, b64s, _, err := client.Generate()
	if err != nil {
		response.ReturnError(c, response.UNKNOWN, "验证码生成失败")
		zap.L().Error("验证码生成失败", zap.Error(err))
		return
	}
	response.ReturnData(c, gin.H{
		"id":    id,
		"image": b64s,
	})
}
````

## File: art-design-pro-edge-go-server-main/api/app/system/user/info.go
````go
package user

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"api-server/api/middleware"
	"api-server/api/response"
	"api-server/db/pgdb/system"
)

func UpdateUserInfo(c *gin.Context) {
	params := &struct {
		Password string `json:"password" form:"password"`
		Name     string `json:"name" form:"name" binding:"required"`
		Phone    string `json:"phone" form:"phone" binding:"required"`
		Gender   uint   `json:"gender" form:"gender" binding:"required"`
	}{}
	if !middleware.CheckParam(params, c) {
		return
	}
	uID := c.GetString(middleware.JWTDataKey)
	if uID == "" {
		response.ReturnError(c, response.UNAUTHENTICATED, "未携带 token")
		return
	}
	id, err := strconv.ParseUint(uID, 10, 64)
	if err != nil {
		response.ReturnError(c, response.UNAUTHENTICATED, "无效的用户ID")
		return
	}
	u := system.SystemUser{
		Model:  gorm.Model{ID: uint(id)},
		Name:   params.Name,
		Phone:  params.Phone,
		Gender: params.Gender,
	}
	if params.Password != "" {
		u.Password = params.Password
	}
	if err := system.UpdateUser(&u); err != nil {
		response.ReturnError(c, response.DATA_LOSS, "更新用户失败")
		return
	}
	response.ReturnData(c, "更新用户成功")
}

func GetUserInfo(c *gin.Context) {
	uID := c.GetString(middleware.JWTDataKey)
	if uID == "" {
		response.ReturnError(c, response.UNAUTHENTICATED, "未携带 token")
		return
	}
	id, err := strconv.ParseUint(uID, 10, 64)
	if err != nil {
		response.ReturnError(c, response.UNAUTHENTICATED, "无效的用户ID")
		return
	}
	user := system.SystemUser{Model: gorm.Model{ID: uint(id)}}
	if err := system.GetUser(&user); err != nil {
		response.ReturnError(c, response.DATA_LOSS, "查询用户失败")
		return
	}
	user.Password = "" // 不返回密码
	response.ReturnData(c, user)
}
````

## File: art-design-pro-edge-go-server-main/api/app/system/user/login.go
````go
package user

import (
	"fmt"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"api-server/api/middleware"
	"api-server/api/response"
	"api-server/db/pgdb/system"
	"api-server/db/rdb/captcha"
	"api-server/util/authentication"
)

func Login(c *gin.Context) {
	params := &struct {
		Username  string `json:"username" form:"username" binding:"required"`
		Password  string `json:"password" form:"password" binding:"required"`
		Captcha   string `json:"captcha" form:"captcha" binding:"required"`
		CaptchaID string `json:"captcha_id" form:"captcha_id" binding:"required"`
	}{}
	if !middleware.CheckParam(params, c) {
		return
	}
	// 验证验证码
	captchaVerify := captcha.GetRedisStore().Verify(params.CaptchaID, params.Captcha, true)
	if !captchaVerify {
		response.ReturnError(c, response.INVALID_ARGUMENT, "验证码错误")
		return
	}

	// 获取客户端IP
	clientIP := c.ClientIP()
	log := system.SystemUserLoginLog{
		UserName: params.Username,
		Password: params.Password,
		IP:       clientIP,
	}

	// 查询用户
	user, err := system.VerifyUser(params.Username, params.Password)
	if err != nil {
		zap.L().Error("查询用户失败", zap.Error(err))
		// 记录登录失败日志（验证码正确但查询失败）
		system.CreateLoginLog(&log)
		response.ReturnError(c, response.DATA_LOSS, "查询用户失败")
		return
	}
	if user.ID == 0 {
		system.CreateLoginLog(&log)
		response.ReturnError(c, response.INVALID_ARGUMENT, "账号或密码错误")
		return
	}
	if user.Status != 1 {
		system.CreateLoginLog(&log)
		response.ReturnError(c, response.INVALID_ARGUMENT, "账号已被禁用")
		return
	}

	// 记录登录成功日志
	log.Password = "" // 不记录密码
	system.CreateLoginLog(&log)
	// 生成token
	token, err := authentication.JWTIssue(fmt.Sprintf("%d", user.ID))
	if err != nil {
		zap.L().Error("生成token失败", zap.Error(err))
		response.ReturnError(c, response.INTERNAL, "生成token失败")
		return
	}
	response.ReturnData(c, gin.H{
		"access_token": token,
	})
}

func FindLoginLogList(c *gin.Context) {
	params := &struct {
		IP       string `json:"ip" form:"ip"`
		Username string `json:"username" form:"username"`
	}{}
	if !middleware.CheckParam(params, c) {
		return
	}
	// 获取分页参数
	page := middleware.GetPage(c)
	pageSize := middleware.GetPageSize(c)
	log := system.SystemUserLoginLog{
		IP:       params.IP,
		UserName: params.Username,
	}
	logs, total, err := system.FindLoginLogList(&log, page, pageSize)
	if err != nil {
		zap.L().Error("查询登录日志失败", zap.Error(err))
		response.ReturnError(c, response.DATA_LOSS, "查询登录日志失败")
		return
	}
	response.ReturnDataWithCount(c, int(total), logs)
}
````

## File: art-design-pro-edge-go-server-main/api/app/system/user/menu.go
````go
package user

import (
	"strconv"

	"github.com/gin-gonic/gin"

	"api-server/api/middleware"
	"api-server/api/response"
	"api-server/common/menu"
	"api-server/db/pgdb/system"
)

func GetUserMenuList(c *gin.Context) {
	// 获取用户ID
	uID := c.GetString(middleware.JWTDataKey)
	if uID == "" {
		response.ReturnError(c, response.UNAUTHENTICATED, "未携带 token")
		return
	}
	id, err := strconv.ParseUint(uID, 10, 64)
	if err != nil {
		response.ReturnError(c, response.UNAUTHENTICATED, "无效的用户ID")
		return
	}

	// 根据用户ID获取用户角色的菜单权限
	roleMenus, rolePermissions, err := system.GetUserMenuData(uint(id))
	if err != nil {
		response.ReturnError(c, response.DATA_LOSS, "查询用户菜单失败")
		return
	}

	// 提取用户拥有的菜单ID（因为用户已经有所有这些角色菜单的权限）
	var roleMenuIds []uint
	for _, m := range roleMenus {
		roleMenuIds = append(roleMenuIds, m.ID)
	}

	// 提取用户拥有的权限ID
	var roleAuthIds []uint
	for _, a := range rolePermissions {
		roleAuthIds = append(roleAuthIds, a.ID)
	}

	// 构建菜单树 - 使用带权限标记的菜单树构建函数
	menuTree := menu.BuildMenuTreeWithPermission(roleMenus, rolePermissions, roleMenuIds, roleAuthIds, false)
	response.ReturnData(c, menuTree)
}
````

## File: art-design-pro-edge-go-server-main/api/app/system/user/user.go
````go
package user

import (
	"strings"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"api-server/api/middleware"
	"api-server/api/response"
	"api-server/db/pgdb/system"
	"api-server/db/rdb/systemuser"
)

func FindUserByCache(c *gin.Context) {
	params := &struct {
		Username string `json:"username" form:"username"`
		Name     string `json:"name" form:"name"`
		ID       uint   `json:"id" form:"id"`
	}{}
	if !middleware.CheckParam(params, c) {
		return
	}

	// 如果提供了ID，则获取单个用户信息
	if params.ID > 0 {
		userInfo, err := systemuser.GetUserFromCache(params.ID)
		if err != nil {
			response.ReturnError(c, response.DATA_LOSS, "获取用户缓存数据失败")
			return
		}
		response.ReturnData(c, userInfo)
		return
	}

	// 获取分页参数
	page := middleware.GetPage(c)
	pageSize := middleware.GetPageSize(c)

	// 获取所有用户列表
	userList, err := systemuser.GetAllUsersFromCache()
	if err != nil {
		response.ReturnError(c, response.DATA_LOSS, "获取用户缓存列表失败")
		return
	}

	// 过滤结果
	var filteredList []systemuser.UserCacheInfo
	if params.Username != "" || params.Name != "" {
		for _, user := range userList {
			// 如果提供了用户名，且不匹配，则跳过
			if params.Username != "" && !strings.Contains(user.Username, params.Username) {
				continue
			}

			// 如果提供了名称，且不匹配，则跳过
			if params.Name != "" && !strings.Contains(user.Name, params.Name) {
				continue
			}

			// 所有条件都匹配，添加到结果列表
			filteredList = append(filteredList, user)
		}
	} else {
		filteredList = userList
	}

	// 计算总数
	total := len(filteredList)

	// 应用分页
	start := (page - 1) * pageSize
	end := start + pageSize
	if start >= total {
		// 如果起始位置超出了总数，返回空列表
		response.ReturnDataWithCount(c, total, []systemuser.UserCacheInfo{})
		return
	}
	if end > total {
		end = total
	}

	pagedList := filteredList[start:end]
	response.ReturnDataWithCount(c, total, pagedList)
}

func FindUser(c *gin.Context) {
	params := &struct {
		Username     string `json:"username" form:"username"`
		Name         string `json:"name" form:"name"`
		Phone        string `json:"phone" form:"phone"`
		DepartmentID uint   `json:"department_id" form:"department_id"`
		RoleID       uint   `json:"role_id" form:"role_id"`
	}{}
	if !middleware.CheckParam(params, c) {
		return
	}
	page := middleware.GetPage(c)
	pageSize := middleware.GetPageSize(c)
	u := system.SystemUser{
		Username:     params.Username,
		Name:         params.Name,
		Phone:        params.Phone,
		RoleID:       params.RoleID,
		DepartmentID: params.DepartmentID,
	}
	usersWithRelations, total, err := system.FindUserList(&u, page, pageSize)
	if err != nil {
		response.ReturnError(c, response.DATA_LOSS, "查询用户失败")
		return
	}

	// 使用索引方式清空密码
	for i := range usersWithRelations {
		usersWithRelations[i].SystemUser.Password = ""
	}

	response.ReturnDataWithCount(c, int(total), usersWithRelations)
}

func AddUser(c *gin.Context) {
	params := &struct {
		Username     string `json:"username" form:"username" binding:"required"`
		Password     string `json:"password" form:"password" binding:"required"`
		Name         string `json:"name" form:"name" binding:"required"`
		Phone        string `json:"phone" form:"phone" binding:"required"`
		Gender       uint   `json:"gender" form:"gender" binding:"required"`
		Status       uint   `json:"status" form:"status" binding:"required"`
		RoleID       uint   `json:"role_id" form:"role_id" binding:"required"`
		DepartmentID uint   `json:"department_id" form:"department_id" binding:"required"`
	}{}
	if !middleware.CheckParam(params, c) {
		return
	}
	u := system.SystemUser{
		Username:     params.Username,
		Password:     params.Password,
		Name:         params.Name,
		Phone:        params.Phone,
		Gender:       params.Gender,
		Status:       params.Status,
		RoleID:       params.RoleID,
		DepartmentID: params.DepartmentID,
	}
	if err := system.AddUser(&u); err != nil {
		response.ReturnError(c, response.DATA_LOSS, "添加用户失败")
		return
	}
	response.ReturnData(c, nil)
}

func UpdateUser(c *gin.Context) {
	params := &struct {
		ID           uint   `json:"id" form:"id" binding:"required"`
		Username     string `json:"username" form:"username" binding:"required"`
		Password     string `json:"password" form:"password"`
		Name         string `json:"name" form:"name" binding:"required"`
		Phone        string `json:"phone" form:"phone" binding:"required"`
		Gender       uint   `json:"gender" form:"gender" binding:"required"`
		Status       uint   `json:"status" form:"status" binding:"required"`
		RoleID       uint   `json:"role_id" form:"role_id" binding:"required"`
		DepartmentID uint   `json:"department_id" form:"department_id" binding:"required"`
	}{}
	if !middleware.CheckParam(params, c) {
		return
	}
	u := system.SystemUser{
		Model:        gorm.Model{ID: params.ID},
		Username:     params.Username,
		Name:         params.Name,
		Phone:        params.Phone,
		Gender:       params.Gender,
		Status:       params.Status,
		RoleID:       params.RoleID,
		DepartmentID: params.DepartmentID,
	}
	if params.Password != "" {
		u.Password = params.Password
	}
	if err := system.UpdateUser(&u); err != nil {
		response.ReturnError(c, response.DATA_LOSS, "更新用户失败")
		return
	}
	response.ReturnData(c, nil)
}

func DeleteUser(c *gin.Context) {
	params := &struct {
		ID uint `json:"id" form:"id" binding:"required"`
	}{}
	if !middleware.CheckParam(params, c) {
		return
	}
	if params.ID == 1 {
		response.ReturnError(c, response.DATA_LOSS, "不能删除超级管理员")
		return
	}
	u := system.SystemUser{
		Model: gorm.Model{ID: params.ID},
	}
	if err := system.DeleteUser(&u); err != nil {
		response.ReturnError(c, response.DATA_LOSS, "删除用户失败")
		return
	}
	response.ReturnData(c, nil)
}
````

## File: art-design-pro-edge-go-server-main/api/middleware/cross-domain.go
````go
package middleware

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// CorssDomainHandler consent cross-domain middleware
func CorssDomainHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		method := c.Request.Method               // method
		origin := c.Request.Header.Get("Origin") // header
		if origin != "" {
			c.Writer.Header().Set("Access-Control-Allow-Origin", "*")
			c.Header("Access-Control-Allow-Origin", "*")  // This is to allow access to all domains
			c.Header("Access-Control-Allow-Methods", "*") // All cross-domain request methods supported by the server, in order to avoid multiple'pre-check' requests for browsing requests
			//  header
			c.Header("Access-Control-Allow-Headers", "*")
			c.Header("Access-Control-Expose-Headers", "*")
			c.Header("Access-Control-Max-Age", "172800")
			c.Header("Access-Control-Allow-Credentials", "false")
			c.Set("content-type", "application/json")
		}

		// Release all OPTIONS methods
		if method == "OPTIONS" {
			c.JSON(http.StatusOK, "Options Request!")
		}
		// Processing request
		c.Next()
	}
}
````

## File: art-design-pro-edge-go-server-main/api/middleware/jwt.go
````go
package middleware

import (
	"github.com/gin-gonic/gin"

	"api-server/api/response"
	"api-server/util/authentication"
)

const (
	JWTDataKey = "jwtData"
)

// TokenVerify Get the token and verify its validity
func TokenVerify(c *gin.Context) {
	c.FormFile("file") // 防止文件未发送完成就返回错误, 导致前端504而不是正确响应
	token := c.Request.Header.Get("Access-Token")
	if token == "" {
		response.ReturnError(c, response.UNAUTHENTICATED, "未携带 token")
		return
	}
	data, err := authentication.JWTDecrypt(token)
	if err != nil {
		response.ReturnError(c, response.UNAUTHENTICATED, "token 解析失败")
		return
	}
	// set data to gin.Context
	c.Set(JWTDataKey, data)
	// Next
	c.Next()
}
````

## File: art-design-pro-edge-go-server-main/api/middleware/page.go
````go
package middleware

import (
	"strconv"

	"api-server/config"

	"github.com/gin-gonic/gin"
)

func GetPage(c *gin.Context) int {
	p, err := strconv.Atoi(c.Query("page"))
	if err != nil {
		return config.DefaultPage
	}
	return p
}

func GetPageSize(c *gin.Context) int {
	ps, err := strconv.Atoi(c.Query("pageSize"))
	if err != nil {
		return config.DefaultPageSize
	}
	return ps
}
````

## File: art-design-pro-edge-go-server-main/api/middleware/params.go
````go
package middleware

import (
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"

	"api-server/api/response"
)

// 校验参数
func CheckParam(params interface{}, c *gin.Context) bool {
	if err := c.ShouldBindWith(params, binding.Default(c.Request.Method, c.ContentType())); err != nil {
		response.ReturnError(c, response.INVALID_ARGUMENT, err.Error())
		return false
	}
	return true
}
````

## File: art-design-pro-edge-go-server-main/api/response/code.go
````go
package response

// https://google-cloud.gitbook.io/api-design-guide/errors

type responseData struct {
	Code      int         `json:"code"`
	Status    string      `json:"status"`
	Message   string      `json:"message,omitempty"`
	Data      interface{} `json:"data,omitempty"`
	Timestamp int64       `json:"timestamp"`
	Count     *int        `json:"count,omitempty"`
}

// 通用的错误信息

// 没有错误。
var Success = responseData{
	Code:    200,
	Status:  "OK",
	Message: "请求成功",
}

// 客户端发送的数据包含非法参数。查看错误消息和错误详情来获取更多的信息。
var INVALID_ARGUMENT = responseData{
	Code:    400,
	Status:  "INVALID_ARGUMENT",
	Message: "请求参数错误",
}

// 现在的系统状态不可以执行当前的请求，例如删除一个非空的目录。
var FAILED_PRECONDITION = responseData{
	Code:    400,
	Status:  "FAILED_PRECONDITION",
	Message: "无法执行客户端请求",
}

// 客户端指定了一个非法的范围。
var OUT_OF_RANGE = responseData{
	Code:    400,
	Status:  "OUT_OF_RANGE",
	Message: "客户端越限访问",
}

// 因为缺失的，失效的或者过期的OAuth令牌，请求未能通过身份认证。
var UNAUTHENTICATED = responseData{
	Code:    401,
	Status:  "UNAUTHENTICATED",
	Message: "身份验证失败",
}

// 客户端没有足够的权限。这可能是因为OAuth令牌没有正确的作用域，或者客户端没有权限，或者是API对客户端代码禁用了。
var PERMISSION_DENIED = responseData{
	Code:    403,
	Status:  "PERMISSION_DENIED",
	Message: "客户端权限不足",
}

// 特定的资源没有被找到或者请求因为某些未被公开的原因拒绝（例如白名单）。
var NOT_FOUND = responseData{
	Code:    404,
	Status:  "NOT_FOUND",
	Message: "资源不存在",
}

// 并发冲突，如读 - 修改 - 写冲突。
var ABORTED = responseData{
	Code:    409,
	Status:  "ABORTED",
	Message: "数据处理冲突",
}

// 客户端尝试新建的资源已经存在了。
var ALREADY_EXISTS = responseData{
	Code:    409,
	Status:  "ALREADY_EXISTS",
	Message: "资源已存在",
}

// 资源配额不足或达不到速率限制。
var RESOURCE_EXHAUSTED = responseData{
	Code:    429,
	Status:  "RESOURCE_EXHAUSTED",
	Message: "资源配额不足或达不到速率限制",
}

// 请求被客户端取消了。
var CANCELLED = responseData{
	Code:    499,
	Status:  "CANCELLED",
	Message: "请求被客户端取消了",
}

// 不可恢复的数据丢失或数据损坏。 客户端应该向用户报告错误。
var DATA_LOSS = responseData{
	Code:    500,
	Status:  "DATA_LOSS",
	Message: "处理数据发生错误",
}

// 未知的服务端出错，通常是由于服务器出现bug了。
var UNKNOWN = responseData{
	Code:    500,
	Status:  "UNKNOWN",
	Message: "服务器未知错误",
}

// 服务器内部错误。通常是由于服务器出现bug了。
var INTERNAL = responseData{
	Code:    500,
	Status:  "INTERNAL",
	Message: "服务器内部错误",
}

// API方法没有被服务器实现。
var NOT_IMPLEMENTED = responseData{
	Code:    501,
	Status:  "NOT_IMPLEMENTED",
	Message: "API不存在",
}

// 服务不可用。通常是由于服务器宕机了。
var UNAVAILABLE = responseData{
	Code:    503,
	Status:  "UNAVAILABLE",
	Message: "服务不可用",
}

// 请求超过了截止日期。 只有当调用者设置的截止日期比方法的默认截止日期更短（服务器没能够在截止日期之前处理完请求）并且请求没有在截止日期内完成时，才会发生这种情况。
var DEALINE_EXCEED = responseData{
	Code:    504,
	Status:  "DEALINE_EXCEED",
	Message: "请求超时",
}

// 根据业务自定义的常用状态码
````

## File: art-design-pro-edge-go-server-main/api/response/format.go
````go
package response

import (
	"fmt"
	"net/http"
	"reflect"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// 递归处理数据结构，转换时间和ID字段
func processData(data interface{}) interface{} {
	if data == nil {
		return nil
	}

	value := reflect.ValueOf(data)

	// 处理指针
	if value.Kind() == reflect.Ptr {
		if value.IsNil() {
			return nil
		}
		return processData(value.Elem().Interface())
	}

	switch value.Kind() {
	case reflect.Struct:
		// 处理time.Time类型
		if t, ok := data.(time.Time); ok {
			return t.Unix()
		}

		// 创建一个新的map来存储处理后的结构体字段
		result := make(map[string]interface{})

		// 处理结构体的字段
		t := value.Type()
		for i := 0; i < t.NumField(); i++ {
			field := t.Field(i)

			// 跳过未导出的字段
			if !field.IsExported() {
				continue
			}

			// 获取json标签
			jsonTag := field.Tag.Get("json")
			if jsonTag == "-" {
				continue
			}

			// 解析json标签获取字段名
			jsonName := field.Name
			if jsonTag != "" {
				parts := strings.Split(jsonTag, ",")
				if parts[0] != "" {
					jsonName = parts[0]
				}
			}

			fieldValue := value.Field(i).Interface()

			// 特别处理gorm.Model
			if field.Name == "Model" {
				// 检查是否为gorm.Model类型
				modelType := reflect.TypeOf(gorm.Model{})
				if reflect.TypeOf(fieldValue).ConvertibleTo(modelType) {
					model := fieldValue.(gorm.Model)
					// 添加小写id字段
					result["id"] = model.ID
					// 转换时间字段为时间戳
					result["created_at"] = model.CreatedAt.Unix()
					result["updated_at"] = model.UpdatedAt.Unix()
					if !model.DeletedAt.Time.IsZero() {
						result["deleted_at"] = model.DeletedAt.Time.Unix()
					}
					continue
				}
			}

			// 递归处理其他字段
			result[jsonName] = processData(fieldValue)
		}
		return result

	case reflect.Slice, reflect.Array:
		// Handle []byte specifically to convert to string
		if value.Type().Elem().Kind() == reflect.Uint8 {
			// Check if the element type is uint8 (byte)
			// For json.RawMessage or []byte that should be a JSON string or object,
			// you might need to unmarshal it first if you want it as a map/slice in the output,
			// or simply convert to string if it's intended to be a string.
			// Assuming it's intended to be a string representation of the bytes:
			return string(value.Bytes())
		}
		// 处理切片和数组
		resultSlice := make([]interface{}, value.Len())
		for i := 0; i < value.Len(); i++ {
			resultSlice[i] = processData(value.Index(i).Interface())
		}
		return resultSlice

	case reflect.Map:
		// 处理映射
		resultMap := make(map[string]interface{})
		keys := value.MapKeys()
		for _, key := range keys {
			keyStr := fmt.Sprintf("%v", key.Interface())
			resultMap[keyStr] = processData(value.MapIndex(key).Interface())
		}
		return resultMap

	default:
		// 其他类型直接返回
		return data
	}
}

func ReturnErrorWithData(c *gin.Context, data responseData, result interface{}) {
	data.Timestamp = time.Now().Unix()
	data.Data = processData(result)
	c.JSON(http.StatusOK, data)
	// Return directly
	c.Abort()
}

// ResponseData 正常响应
func ReturnData(c *gin.Context, result interface{}) {
	data := Success
	data.Timestamp = time.Now().Unix()
	data.Data = processData(result)
	c.JSON(http.StatusOK, data)
	// Return directly
	c.Abort()
}

// ResponseDataWithCount 正常响应
func ReturnDataWithCount(c *gin.Context, count int, result interface{}) {
	data := Success
	data.Timestamp = time.Now().Unix()
	data.Data = processData(result)
	data.Count = &count
	c.JSON(http.StatusOK, data)
	// Return directly
	c.Abort()
}

// ResponseError 错误响应
func ReturnError(c *gin.Context, data responseData, description string) {
	data.Timestamp = time.Now().Unix()
	data.Message = func() string {
		if description == "" {
			return data.Message
		}
		return description
	}()
	c.JSON(http.StatusOK, data)
	// Return directly
	c.Abort()
}

// ResponseSuccess 执行成功
func ReturnSuccess(c *gin.Context) {
	data := Success
	data.Timestamp = time.Now().Unix()
	c.JSON(http.StatusOK, data)
	// Return directly
	c.Abort()
}
````

## File: art-design-pro-edge-go-server-main/api/router.go
````go
package api

import (
	"github.com/gin-gonic/gin"

	"api-server/api/app/system/department"
	"api-server/api/app/system/menu"
	"api-server/api/app/system/role"
	"api-server/api/app/system/user"
	"api-server/api/middleware"
)

func systemRouter(router *gin.RouterGroup) {
	systemRouter := router.Group("/admin/system")
	{
		systemRouter.GET("/user/login/captcha", user.GetCaptcha)
		systemRouter.POST("/user/login", user.Login)
		systemRouter.GET("/login/log", middleware.TokenVerify, user.FindLoginLogList)
		systemRouter.GET("/user/info", middleware.TokenVerify, user.GetUserInfo)
		systemRouter.PUT("/user/info", middleware.TokenVerify, user.UpdateUserInfo)
		systemRouter.GET("/user/menu", middleware.TokenVerify, user.GetUserMenuList)
		systemRouter.GET("/menu", middleware.TokenVerify, menu.GetMenuList)
		systemRouter.POST("/menu", middleware.TokenVerify, menu.AddMenu)
		systemRouter.DELETE("/menu", middleware.TokenVerify, menu.DeleteMenu)
		systemRouter.PUT("/menu", middleware.TokenVerify, menu.UpdateMenu)
		systemRouter.GET("/menu/auth", middleware.TokenVerify, menu.GetMenuAuthList)
		systemRouter.POST("/menu/auth", middleware.TokenVerify, menu.AddMenuAuth)
		systemRouter.DELETE("/menu/auth", middleware.TokenVerify, menu.DeleteMenuAuth)
		systemRouter.PUT("/menu/auth", middleware.TokenVerify, menu.UpdateMenuAuth)
		systemRouter.GET("/menu/role", middleware.TokenVerify, menu.GetMenuListByRoleID)
		systemRouter.PUT("/menu/role", middleware.TokenVerify, menu.UpdateMenuListByRoleID)
		systemRouter.GET("/department", middleware.TokenVerify, department.GetDepartmentList)
		systemRouter.POST("/department", middleware.TokenVerify, department.AddDepartment)
		systemRouter.PUT("/department", middleware.TokenVerify, department.UpdateDepartment)
		systemRouter.DELETE("/department", middleware.TokenVerify, department.DeleteDepartment)
		systemRouter.GET("/role", middleware.TokenVerify, role.GetRoleList)
		systemRouter.POST("/role", middleware.TokenVerify, role.AddRole)
		systemRouter.PUT("/role", middleware.TokenVerify, role.UpdateRole)
		systemRouter.DELETE("/role", middleware.TokenVerify, role.DeleteRole)
		systemRouter.GET("/user", middleware.TokenVerify, user.FindUser)
		systemRouter.GET("/user/cache", middleware.TokenVerify, user.FindUserByCache)
		systemRouter.POST("/user", middleware.TokenVerify, user.AddUser)
		systemRouter.PUT("/user", middleware.TokenVerify, user.UpdateUser)
		systemRouter.DELETE("/user", middleware.TokenVerify, user.DeleteUser)
	}
}

// InitApi init gshop app
func InitApi() *gin.Engine {
	// gin.Default uses Use by default. Two global middlewares are added, Logger(), Recovery(), Logger is to print logs, Recovery is panic and returns 500
	gin.SetMode(gin.ReleaseMode)
	router := gin.Default()
	// https://pkg.go.dev/github.com/gin-gonic/gin#readme-don-t-trust-all-proxies
	router.SetTrustedProxies(nil)
	// Add consent cross-domain middleware
	router.Use(middleware.CorssDomainHandler())
	// static
	router.Static("/static", "./static")
	// api-v1
	v1 := router.Group("/api/v1")
	{
		systemRouter(v1)
	}
	return router
}
````

## File: art-design-pro-edge-go-server-main/common/cron/manager.go
````go
package cron

import (
	"time"

	"github.com/go-co-op/gocron/v2"
	"go.uber.org/zap"
)

var scheduler gocron.Scheduler

// InitCronJobs 初始化所有定时任务
func InitCronJobs() {
	var err error
	// 创建一个带时区的调度器
	scheduler, err = gocron.NewScheduler(gocron.WithLocation(time.Local))
	if err != nil {
		zap.L().Error("创建定时任务调度器失败", zap.Error(err))
		return
	}

	// 初始化用户缓存定时任务
	InitUserCacheJob()

	// 启动调度器
	scheduler.Start()

	zap.L().Info("定时任务调度器已启动")
}
````

## File: art-design-pro-edge-go-server-main/common/cron/user_cache.go
````go
package cron

import (
	"time"

	"github.com/go-co-op/gocron/v2"
	"go.uber.org/zap"

	"api-server/db/rdb/systemuser"
)

// InitUserCacheJob 初始化用户缓存定时任务
func InitUserCacheJob() {
	// 立即执行一次缓存
	if err := systemuser.CacheAllUsers(); err != nil {
		zap.L().Error("初始化用户缓存失败", zap.Error(err))
	} else {
		zap.L().Info("初始化用户缓存成功")
	}

	// 每10分钟执行一次用户信息缓存更新
	job, err := scheduler.NewJob(
		gocron.DurationJob(
			10*60*time.Second, // 10分钟
		),
		gocron.NewTask(
			func() {
				zap.L().Info("开始执行用户缓存定时更新")
				if err := systemuser.CacheAllUsers(); err != nil {
					zap.L().Error("更新用户缓存失败", zap.Error(err))
				} else {
					zap.L().Info("更新用户缓存成功")
				}
			},
		),
	)

	if err != nil {
		zap.L().Error("创建用户缓存定时任务失败", zap.Error(err))
	} else {
		zap.L().Info("用户缓存定时任务已创建，每10分钟执行一次", zap.String("jobID", job.ID().String()))
	}
}
````

## File: art-design-pro-edge-go-server-main/common/menu/tree.go
````go
package menu

import (
	"sort"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"api-server/db/pgdb"
	"api-server/db/pgdb/system"
)

// MenuResponse 定义返回给前端的菜单结构
type MenuResponse struct {
	ID            uint           `json:"id"`
	UpdatedAt     uint           `json:"updatedAt,omitempty"`
	Path          string         `json:"path"`
	Name          string         `json:"name"`
	Component     string         `json:"component,omitempty"`
	Meta          MenuMeta       `json:"meta"`
	Children      []MenuResponse `json:"children,omitempty"`
	ParentID      uint           `json:"parentId,omitempty"`
	HasPermission bool           `json:"hasPermission"` // 角色是否拥有此菜单权限
}

// MenuMeta 定义菜单元数据
type MenuMeta struct {
	Title         string         `json:"title"`
	Icon          string         `json:"icon,omitempty"`
	KeepAlive     bool           `json:"keepAlive"`
	ShowBadge     bool           `json:"showBadge,omitempty"`
	ShowTextBadge string         `json:"showTextBadge,omitempty"`
	IsHide        bool           `json:"isHide,omitempty"`
	IsHideTab     bool           `json:"isHideTab,omitempty"`
	Link          string         `json:"link,omitempty"`
	IsIframe      bool           `json:"isIframe,omitempty"`
	IsFirstLevel  bool           `json:"isFirstLevel,omitempty"`
	IsEnable      bool           `json:"isEnable,omitempty"`
	Sort          uint           `json:"sort,omitempty"`
	AuthList      []MenuAuthResp `json:"authList,omitempty"`
}

// 定义菜单权限响应结构
type MenuAuthResp struct {
	ID            uint   `json:"id"`
	Title         string `json:"title"`
	AuthMark      string `json:"auth_mark"`     // 权限标识
	HasPermission bool   `json:"hasPermission"` // 角色是否拥有此权限
}

// 构建菜单树
// all: 是否包含所有菜单，true表示包含所有菜单，false表示只包含启用的菜单
func BuildMenuTree(menus []system.SystemMenu, permissions []system.SystemMenuAuth, all bool) []MenuResponse {
	var menuTree []MenuResponse

	// 找出所有顶级菜单(ParentID = 0)
	var rootMenus []system.SystemMenu
	if len(menus) > 0 {
		for _, menu := range menus {
			if menu.ParentID == 0 {
				if !all {
					if menu.Status == 2 {
						continue
					}
				}
				rootMenus = append(rootMenus, menu)
			}
		}
	}

	// 对根菜单按 Sort 从大到小排序，Sort 为 0 则按 ID 从大到小
	sortMenus(rootMenus)

	// 递归构建菜单树
	for _, rootMenu := range rootMenus {
		menuResp := convertMenuToResponse(rootMenu)
		buildMenuChildren(&menuResp, menus, permissions, all)
		menuTree = append(menuTree, menuResp)
	}

	return menuTree
}

// 构建带有权限标记的菜单树
func BuildMenuTreeWithPermission(menus []system.SystemMenu, permissions []system.SystemMenuAuth, roleMenuIds []uint, roleAuthIds []uint, all bool) []MenuResponse {
	var menuTree []MenuResponse

	// 找出所有顶级菜单(ParentID = 0)
	var rootMenus []system.SystemMenu
	if len(menus) > 0 {
		for _, menu := range menus {
			if menu.ParentID == 0 {
				if !all && menu.Status == 2 {
					continue
				}
				rootMenus = append(rootMenus, menu)
			}
		}
	}

	// 对根菜单按 Sort 从大到小排序
	sortMenus(rootMenus)

	// 递归构建菜单树
	for _, rootMenu := range rootMenus {
		menuResp := convertMenuToResponseWithPermission(rootMenu, roleMenuIds)
		buildMenuChildrenWithPermission(&menuResp, menus, permissions, roleMenuIds, roleAuthIds, all)
		menuTree = append(menuTree, menuResp)
	}

	return menuTree
}

// 将数据库菜单转换为响应结构
func convertMenuToResponse(menu system.SystemMenu) MenuResponse {
	return MenuResponse{
		ID:        menu.ID,
		UpdatedAt: uint(menu.UpdatedAt.Unix()),
		Path:      menu.Path,
		Name:      menu.Name,
		Component: menu.Component,
		ParentID:  menu.ParentID,
		Meta: MenuMeta{
			Title:         menu.Title,
			Icon:          menu.Icon,
			KeepAlive:     menu.KeepAlive == 1, // 1表示缓存
			ShowBadge:     menu.ShowBadge == 1, // 1表示显示
			ShowTextBadge: menu.ShowTextBadge,
			IsHide:        menu.IsHide == 1,    // 1表示隐藏
			IsHideTab:     menu.IsHideTab == 1, // 1表示隐藏
			Link:          menu.Link,
			IsIframe:      menu.IsIframe == 1,     // 1表示是iframe
			IsFirstLevel:  menu.IsFirstLevel == 1, // 1表示在主容器中
			IsEnable:      menu.Status == 1,       // 1表示启用
			Sort:          menu.Sort,
		},
	}
}

// 将数据库菜单转换为响应结构，并标记是否有权限
func convertMenuToResponseWithPermission(menu system.SystemMenu, roleMenuIds []uint) MenuResponse {
	resp := convertMenuToResponse(menu)
	resp.HasPermission = containsUint(roleMenuIds, menu.ID)
	return resp
}

// 递归构建菜单子项
func buildMenuChildren(parent *MenuResponse, allMenus []system.SystemMenu, allPermissions []system.SystemMenuAuth, all bool) {
	var childMenus []system.SystemMenu

	// 收集当前父菜单下的所有子菜单
	for _, menu := range allMenus {
		if menu.ParentID == parent.ID {
			if !all && menu.Status == 2 {
				continue
			}
			childMenus = append(childMenus, menu)
		}
	}

	// 对子菜单进行排序
	sortMenus(childMenus)

	// 处理排序后的子菜单
	for _, menu := range childMenus {
		child := convertMenuToResponse(menu)

		// 为子菜单添加权限列表
		for _, perm := range allPermissions {
			if perm.MenuID == menu.ID {
				child.Meta.AuthList = append(child.Meta.AuthList, MenuAuthResp{
					ID:       perm.ID,
					Title:    perm.Title,
					AuthMark: perm.Mark,
				})
			}
		}

		// 递归处理这个子菜单的子菜单
		buildMenuChildren(&child, allMenus, allPermissions, all)
		parent.Children = append(parent.Children, child)
	}
}

// 递归构建带权限标记的菜单子项
func buildMenuChildrenWithPermission(parent *MenuResponse, allMenus []system.SystemMenu, allPermissions []system.SystemMenuAuth, roleMenuIds []uint, roleAuthIds []uint, all bool) {
	var childMenus []system.SystemMenu

	// 收集当前父菜单下的所有子菜单
	for _, menu := range allMenus {
		if menu.ParentID == parent.ID {
			if !all && menu.Status == 2 {
				continue
			}
			childMenus = append(childMenus, menu)
		}
	}

	// 对子菜单进行排序
	sortMenus(childMenus)

	// 处理排序后的子菜单
	for _, menu := range childMenus {
		child := convertMenuToResponseWithPermission(menu, roleMenuIds)

		// 为子菜单添加权限列表
		for _, perm := range allPermissions {
			if perm.MenuID == menu.ID {
				authResp := MenuAuthResp{
					ID:            perm.ID,
					Title:         perm.Title,
					AuthMark:      perm.Mark,
					HasPermission: containsUint(roleAuthIds, perm.ID),
				}
				child.Meta.AuthList = append(child.Meta.AuthList, authResp)
			}
		}

		// 递归处理这个子菜单的子菜单
		buildMenuChildrenWithPermission(&child, allMenus, allPermissions, roleMenuIds, roleAuthIds, all)
		parent.Children = append(parent.Children, child)
	}
}

// 对菜单切片按 Sort 从大到小排序，Sort 为 0 则按 ID 从大到小排序
func sortMenus(menus []system.SystemMenu) {
	sort.Slice(menus, func(i, j int) bool {
		// 如果两个菜单都有 Sort 值并且不为 0
		if menus[i].Sort > 0 && menus[j].Sort > 0 {
			// Sort 值不同时，按 Sort 从大到小排序
			if menus[i].Sort != menus[j].Sort {
				return menus[i].Sort > menus[j].Sort // 从大到小排序
			}
			// Sort 值相同时，按 ID 从大到小排序
			return menus[i].ID > menus[j].ID
		}

		// 如果只有其中一个有 Sort 值
		if menus[i].Sort > 0 && menus[j].Sort == 0 {
			return true // i 排在前面
		}

		if menus[i].Sort == 0 && menus[j].Sort > 0 {
			return false // j 排在前面
		}

		// 都没有 Sort 值，按 ID 从大到小排序
		return menus[i].ID > menus[j].ID
	})
}

// 辅助函数：检查uint数组中是否包含特定值
func containsUint(slice []uint, item uint) bool {
	for _, i := range slice {
		if i == item {
			return true
		}
	}
	return false
}

func SaveRoleMenu(roleID uint, menuTree []MenuResponse) error {
	// 从menuTree中提取所有有权限的菜单ID和权限ID
	var menuIDs []uint
	var authIDs []uint

	// 递归提取所有有权限的菜单ID和权限ID
	extractPermissions(menuTree, &menuIDs, &authIDs)

	// 使用事务更新数据库
	return pgdb.GetClient().Transaction(func(tx *gorm.DB) error {
		// 查询角色
		var role system.SystemRole
		if err := tx.First(&role, roleID).Error; err != nil {
			zap.L().Error("failed to find role", zap.Uint("roleID", roleID), zap.Error(err))
			return err
		}

		// 更新角色的菜单关联
		if err := tx.Model(&role).Association("SystemMenus").Clear(); err != nil {
			zap.L().Error("failed to clear role menus", zap.Uint("roleID", roleID), zap.Error(err))
			return err
		}

		if len(menuIDs) > 0 {
			var menus []system.SystemMenu
			if err := tx.Where("id IN ?", menuIDs).Find(&menus).Error; err != nil {
				zap.L().Error("failed to find menus", zap.Uint("roleID", roleID), zap.Uints("menuIDs", menuIDs), zap.Error(err))
				return err
			}
			if err := tx.Model(&role).Association("SystemMenus").Append(&menus); err != nil {
				zap.L().Error("failed to append menus to role", zap.Uint("roleID", roleID), zap.Error(err))
				return err
			}
		}

		// 更新角色的权限关联
		if err := tx.Model(&role).Association("SystemMenuAuths").Clear(); err != nil {
			zap.L().Error("failed to clear role auths", zap.Uint("roleID", roleID), zap.Error(err))
			return err
		}

		if len(authIDs) > 0 {
			var auths []system.SystemMenuAuth
			if err := tx.Where("id IN ?", authIDs).Find(&auths).Error; err != nil {
				zap.L().Error("failed to find menu auths", zap.Uint("roleID", roleID), zap.Uints("authIDs", authIDs), zap.Error(err))
				return err
			}
			if err := tx.Model(&role).Association("SystemMenuAuths").Append(&auths); err != nil {
				zap.L().Error("failed to append auths to role", zap.Uint("roleID", roleID), zap.Error(err))
				return err
			}
		}

		return nil
	})
}

// 递归提取有权限的菜单ID和权限ID
func extractPermissions(menuTree []MenuResponse, menuIDs *[]uint, authIDs *[]uint) {
	for _, menu := range menuTree {
		// 如果菜单有权限，添加菜单ID
		if menu.HasPermission {
			*menuIDs = append(*menuIDs, menu.ID)

			// 检查菜单的权限列表
			for _, auth := range menu.Meta.AuthList {
				if auth.HasPermission {
					*authIDs = append(*authIDs, auth.ID)
				}
			}
		}

		// 递归处理子菜单
		if len(menu.Children) > 0 {
			extractPermissions(menu.Children, menuIDs, authIDs)
		}
	}
}
````

## File: art-design-pro-edge-go-server-main/config/config.go
````go
package config

import (
	"fmt"
	"os"
	"path/filepath"
	"time"

	pathtool "api-server/util/path-tool"
)

// Here are some basic configurations
// These configurations are usually generic
var (
	// listen
	ListenPort = 8080 // api listen port
	// run model
	RunModelKey      = "model"
	RunModel         = ""
	RunModelDevValue = "dev"
	RunModelRelease  = "release"
	// path
	SelfName = filepath.Base(os.Args[0])      // own file name
	AbsPath  = pathtool.GetCurrentDirectory() // current directory
	// log
	LogDir        = filepath.Join(pathtool.GetCurrentDirectory(), "log")   // log directory
	LogPath       = filepath.Join(LogDir, fmt.Sprintf("%s.log", SelfName)) // self log path
	LogMaxSize    = 50                                                     // M
	LogMaxBackups = 3                                                      // backups
	LogMaxAge     = 30                                                     // days
	LogModelDev   = "dev"                                                  // dev model
)

// These configurations need to be modified as needed
var (
	// jWT
	JWTKey        = "CvXPiv34e2474LC5Xj7IP" // TODO 务必在部署前对 key 进行修改
	JWTExpiration = time.Hour * 12
)

// redis
var (
	RedisHost     = "127.0.0.1:6379"        // TODO 修改为自己的 redis 地址
	RedisPassword = "izpXvn894uW2HFbyP5OGr" // TODO 修改为自己的 redis 密码
)

// pgsql
var (
	PgsqlDSN = "host=127.0.0.1 user=postgres password=kL81xnDWo221FHFRX8GnP dbname=server port=5432 sslmode=disable TimeZone=Asia/Shanghai" // TODO 修改为自己的 pgsql 配置
)

// admin config
var (
	AdminPassword = "123456"                // TODO 管理员密码, 务必修改为自己的密码
	PWDSalt       = "rHECMvW3el1zhpdzgx9dY" // TODO 数据库存储密码时的盐, 务必重新生成, 且不可泄露, 不可更改
)

// page config
var (
	DefaultPageSize = 20 // default page size
	DefaultPage     = 1  // default page
	CancelPageSize  = -1 // cancel page size
	CancelPage      = -1 // cancel page
)

func init() {
	pathtool.CreateDir(LogDir)
}
````

## File: art-design-pro-edge-go-server-main/db/pgdb/system/auth.go
````go
package system

import (
	"go.uber.org/zap"

	"api-server/db/pgdb"
)

func GetMenuAuth(auth *SystemMenuAuth) error {
	if err := pgdb.GetClient().Where(auth).First(auth).Error; err != nil {
		zap.L().Error("failed to get menu Auth", zap.Error(err))
		return err
	}
	return nil
}

func DeleteMenuAuth(menuAuth *SystemMenuAuth) error {
	if err := pgdb.GetClient().Delete(&menuAuth).Error; err != nil {
		zap.L().Error("failed to delete menu Auth", zap.Error(err))
		return err
	}
	return nil
}

func AddMenuAuth(menuAuth *SystemMenuAuth) error {
	if err := pgdb.GetClient().Create(&menuAuth).Error; err != nil {
		zap.L().Error("failed to create menu Auth", zap.Error(err))
		return err
	}
	return nil
}

func UpdateMenuAuth(menuAuth *SystemMenuAuth) error {
	if err := pgdb.GetClient().Updates(&menuAuth).Error; err != nil {
		zap.L().Error("failed to update menu Auth", zap.Error(err))
		return err
	}
	return nil
}

func FindMenuAuthList(menuAuth *SystemMenuAuth) ([]SystemMenuAuth, error) {
	var auths []SystemMenuAuth
	if err := pgdb.GetClient().Where(menuAuth).Find(&auths).Error; err != nil {
		zap.L().Error("failed to find menu Auth list", zap.Error(err))
		return nil, err
	}
	return auths, nil
}
````

## File: art-design-pro-edge-go-server-main/db/pgdb/system/department.go
````go
package system

import (
	"go.uber.org/zap"
	"gorm.io/gorm"

	"api-server/config"
	"api-server/db/pgdb"
)

// FindDepartmentList 查询部门列表(带分页)
func FindDepartmentList(department *SystemDepartment, page, pageSize int) ([]SystemDepartment, int64, error) {
	var departments []SystemDepartment
	var total int64
	db := pgdb.GetClient()

	// 构建基础查询
	query := db.Model(&SystemDepartment{})

	// 应用过滤条件
	if department.Name != "" {
		query = query.Where("name LIKE ?", "%"+department.Name+"%")
	}
	if department.Status != 0 {
		query = query.Where("status = ?", department.Status)
	}

	// 获取符合条件的总记录数
	if err := query.Count(&total).Error; err != nil {
		zap.L().Error("failed to count department list", zap.Error(err))
		return nil, 0, err
	}

	// 构建排序和预加载
	queryWithPreload := query.Preload("SystemUsers", func(db *gorm.DB) *gorm.DB {
		return db.Select("id", "name", "department_id", "created_at", "updated_at") // 只选择需要的字段
	}).Order("sort DESC, id DESC")

	// 判断是否需要分页
	if page == config.CancelPage && pageSize == config.CancelPageSize {
		// 不分页，获取所有数据
		if err := queryWithPreload.Find(&departments).Error; err != nil {
			zap.L().Error("failed to find all department list", zap.Error(err))
			return nil, 0, err
		}
	} else {
		// 应用分页并获取数据
		if err := queryWithPreload.Offset((page - 1) * pageSize).
			Limit(pageSize).
			Find(&departments).Error; err != nil {
			zap.L().Error("failed to find department list with pagination", zap.Error(err))
			return nil, 0, err
		}
	}

	return departments, total, nil
}

// GetDepartment 查询单个部门（包含有限的用户信息）
func GetDepartment(department *SystemDepartment) error {
	if err := pgdb.GetClient().Preload("SystemUsers", func(db *gorm.DB) *gorm.DB {
		return db.Select("id", "name", "department_id", "created_at", "updated_at") // 只选择需要的字段
	}).Where(department).First(department).Error; err != nil {
		zap.L().Error("failed to get department", zap.Error(err))
		return err
	}
	return nil
}

func AddDepartment(department *SystemDepartment) error {
	if err := pgdb.GetClient().Create(&department).Error; err != nil {
		zap.L().Error("failed to create department", zap.Error(err))
		return err
	}
	return nil
}

func UpdateDepartment(department *SystemDepartment) error {
	if err := pgdb.GetClient().Updates(&department).Error; err != nil {
		zap.L().Error("failed to update department", zap.Error(err))
		return err
	}
	return nil
}

func DeleteDepartment(department *SystemDepartment) error {
	if err := pgdb.GetClient().Delete(&department).Error; err != nil {
		zap.L().Error("failed to delete department", zap.Error(err))
		return err
	}
	return nil
}
````

## File: art-design-pro-edge-go-server-main/db/pgdb/system/menu.go
````go
package system

import (
	"go.uber.org/zap"
	"gorm.io/gorm"

	"api-server/config"
	"api-server/db/pgdb"
)

// GetUserMenuData 获取用户菜单数据
func GetUserMenuData(userID uint) ([]SystemMenu, []SystemMenuAuth, error) {
	// 获取用户信息及其角色
	var user SystemUser
	if err := pgdb.GetClient().Where(&SystemUser{Model: gorm.Model{ID: userID}}).First(&user).Error; err != nil {
		zap.L().Error("failed to get user", zap.Error(err))
		return nil, nil, err
	}
	// 获取该角色关联的所有菜单(包括权限)
	var role SystemRole
	if err := pgdb.GetClient().Preload("SystemMenus").
		Preload("SystemMenuAuths").
		Where("id = ?", user.RoleID).
		First(&role).Error; err != nil {
		zap.L().Error("failed to get role", zap.Error(err))
		return nil, nil, err
	}
	return role.SystemMenus, role.SystemMenuAuths, nil
}

// 获取菜单树(不带分页)
func GetMenuData() ([]SystemMenu, []SystemMenuAuth, error) {
	var menus []SystemMenu
	if err := pgdb.GetClient().Find(&menus).Error; err != nil {
		zap.L().Error("failed to get menus", zap.Error(err))
		return nil, nil, err
	}
	var Auths []SystemMenuAuth
	if err := pgdb.GetClient().Find(&Auths).Error; err != nil {
		zap.L().Error("failed to get menu Auths", zap.Error(err))
		return nil, nil, err
	}
	return menus, Auths, nil
}

// GetMenuDataByRoleID 获取指定角色ID的菜单和权限数据
func GetMenuDataByRoleID(roleID uint) ([]SystemMenu, []SystemMenuAuth, []uint, []uint, error) {
	// 获取所有菜单
	var allMenus []SystemMenu
	if err := pgdb.GetClient().Find(&allMenus).Error; err != nil {
		zap.L().Error("failed to get all menus", zap.Error(err))
		return nil, nil, nil, nil, err
	}
	// 获取所有权限
	var allAuths []SystemMenuAuth
	if err := pgdb.GetClient().Find(&allAuths).Error; err != nil {
		zap.L().Error("failed to get all menu auths", zap.Error(err))
		return nil, nil, nil, nil, err
	}
	// 获取角色拥有的菜单ID列表
	var role SystemRole
	if err := pgdb.GetClient().Preload("SystemMenus").
		Preload("SystemMenuAuths").
		Where("id = ?", roleID).
		First(&role).Error; err != nil {
		zap.L().Error("failed to get role with menus", zap.Error(err))
		return nil, nil, nil, nil, err
	}
	// 提取角色拥有的菜单ID和权限ID
	var roleMenuIds []uint
	var roleAuthIds []uint
	for _, m := range role.SystemMenus {
		roleMenuIds = append(roleMenuIds, m.ID)
	}
	for _, a := range role.SystemMenuAuths {
		roleAuthIds = append(roleAuthIds, a.ID)
	}
	return allMenus, allAuths, roleMenuIds, roleAuthIds, nil
}

// 新增一个菜单
func AddMenu(menu *SystemMenu) error {
	if err := pgdb.GetClient().Create(&menu).Error; err != nil {
		zap.L().Error("failed to create menu", zap.Error(err))
		return err
	}
	return nil
}

// 删除一个菜单
func DeleteMenu(menu *SystemMenu) error {
	if err := pgdb.GetClient().Delete(&menu).Error; err != nil {
		zap.L().Error("failed to delete menu", zap.Error(err))
		return err
	}
	return nil
}

func UpdateMenu(menu *SystemMenu) error {
	if err := pgdb.GetClient().Omit("created_at").Save(menu).Error; err != nil {
		zap.L().Error("failed to update menu", zap.Error(err))
		return err
	}
	return nil
}

func GetMenu(menu *SystemMenu) error {
	if err := pgdb.GetClient().Where(menu).First(menu).Error; err != nil {
		zap.L().Error("failed to get menu", zap.Error(err))
		return err
	}
	return nil
}

// FindMenuList 查询菜单列表(带分页)
func FindMenuList(menu *SystemMenu, page, pageSize int) ([]SystemMenu, int64, error) {
	var menus []SystemMenu
	var total int64
	db := pgdb.GetClient()

	// 构建基础查询
	query := db.Model(&SystemMenu{})

	// 应用过滤条件
	if menu.Title != "" {
		query = query.Where("title LIKE ?", "%"+menu.Title+"%")
	}
	if menu.Name != "" {
		query = query.Where("name LIKE ?", "%"+menu.Name+"%")
	}
	if menu.Path != "" {
		query = query.Where("path LIKE ?", "%"+menu.Path+"%")
	}
	if menu.ParentID != 0 {
		query = query.Where("parent_id = ?", menu.ParentID)
	}
	if menu.Status != 0 {
		query = query.Where("status = ?", menu.Status)
	}

	// 获取符合条件的总记录数
	if err := query.Count(&total).Error; err != nil {
		zap.L().Error("failed to count menu list", zap.Error(err))
		return nil, 0, err
	}

	// 构建排序
	queryOrder := query.Order("sort DESC, id DESC")

	// 判断是否需要分页
	if page == config.CancelPage && pageSize == config.CancelPageSize {
		// 不分页，获取所有数据
		if err := queryOrder.Find(&menus).Error; err != nil {
			zap.L().Error("failed to find all menu list", zap.Error(err))
			return nil, 0, err
		}
	} else {
		// 应用分页并获取数据
		if err := queryOrder.Offset((page - 1) * pageSize).
			Limit(pageSize).
			Find(&menus).Error; err != nil {
			zap.L().Error("failed to find menu list with pagination", zap.Error(err))
			return nil, 0, err
		}
	}

	return menus, total, nil
}
````

## File: art-design-pro-edge-go-server-main/db/pgdb/system/migrate.go
````go
package system

import (
	"fmt"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"api-server/config"
)

func migrateTable(db *gorm.DB) error {
	err := db.AutoMigrate(&SystemDepartment{}, &SystemRole{}, &SystemMenu{}, &SystemMenuAuth{}, &SystemUser{}, &SystemUserLoginLog{})
	if err != nil {
		zap.L().Error("failed to migrate system model", zap.Error(err))
		return err
	}
	return nil
}

func migrateData(db *gorm.DB) error {
	err := db.Transaction(func(tx *gorm.DB) error {
		// 检查是否已有数据，如果有则跳过初始化
		var count int64
		tx.Model(&SystemMenu{}).Count(&count)
		if count > 0 {
			zap.L().Info("menu data already exists, skipping initial data creation")
			return nil
		}

		// 创建菜单
		menus := []SystemMenu{
			{Model: gorm.Model{ID: 1}, Path: "/dashboard", Name: "Dashboard", Component: "/index/index", Title: "仪表盘", Icon: "&#xe721;", KeepAlive: 2, Status: 1, Level: 1, ParentID: 0, Sort: 99},
			{Model: gorm.Model{ID: 2}, Path: "/system", Name: "System", Component: "/index/index", Title: "系统管理", Icon: "&#xe72b;", KeepAlive: 2, Status: 1, Level: 1, ParentID: 0, Sort: 20},
			{Model: gorm.Model{ID: 3}, Path: "menu", Name: "SystemMenu", Component: "/system/menu/index", Title: "菜单管理", KeepAlive: 2, Status: 1, Level: 2, ParentID: 2, Sort: 99},
			{Model: gorm.Model{ID: 4}, Path: "role", Name: "SystemRole", Component: "/system/role/index", Title: "角色管理", KeepAlive: 2, Status: 1, Level: 2, ParentID: 2, Sort: 88},
			{Model: gorm.Model{ID: 5}, Path: "department", Name: "SystemDepartment", Component: "/system/department/index", Title: "部门管理", KeepAlive: 2, Status: 1, Level: 2, ParentID: 2, Sort: 77},
			{Model: gorm.Model{ID: 6}, Path: "user", Name: "SystemUser", Component: "/system/user/index", Title: "用户管理", KeepAlive: 2, Status: 1, Level: 2, ParentID: 2, Sort: 66},
			{Model: gorm.Model{ID: 7}, Path: "console", Name: "DashboardConsole", Component: "/dashboard/console/index", Title: "工作台", Icon: "", KeepAlive: 2, Status: 1, Level: 2, ParentID: 1, Sort: 99},
			{Model: gorm.Model{ID: 8}, Path: "analysis", Name: "DashboardAnalysis", Component: "/dashboard/analysis/index", Title: "分析页", Icon: "", KeepAlive: 2, Status: 1, Level: 2, ParentID: 1, Sort: 88},
			{Model: gorm.Model{ID: 9}, Path: "/private", Name: "Private", Component: "/index/index", Title: "隐藏页面", Icon: "", KeepAlive: 2, Status: 1, Level: 1, ParentID: 0, Sort: 99, IsHide: 1},
		}
		err := db.Create(&menus).Error
		if err != nil {
			zap.L().Error("failed to create menu", zap.Error(err))
			return err
		}

		// 检查是否已有角色数据
		tx.Model(&SystemRole{}).Count(&count)
		if count > 0 {
			zap.L().Info("role data already exists, skipping role creation")
			return nil
		}

		// 创建角色
		roles := []SystemRole{
			{Model: gorm.Model{ID: 1}, Name: "超级管理员", Desc: "拥有所有权限", Status: 1},
			{Model: gorm.Model{ID: 2}, Name: "普通用户", Desc: "普通用户", Status: 1},
		}
		err = db.Create(&roles).Error
		if err != nil {
			zap.L().Error("failed to create role", zap.Error(err))
			return err
		}

		// 为角色分配菜单权限
		// 超级管理员拥有所有菜单权限
		adminRole := SystemRole{}
		err = db.First(&adminRole, 1).Error
		if err != nil {
			zap.L().Error("failed to find admin role", zap.Error(err))
			return err
		}
		// 为超级管理员分配所有菜单
		var allMenus []SystemMenu
		err = db.Find(&allMenus).Error
		if err != nil {
			zap.L().Error("failed to find menus", zap.Error(err))
			return err
		}
		err = db.Model(&adminRole).Association("SystemMenus").Append(&allMenus)
		if err != nil {
			zap.L().Error("failed to associate menus with admin role", zap.Error(err))
			return err
		}
		// 为普通用户分配首页菜单
		normalRole := SystemRole{}
		err = db.First(&normalRole, 2).Error
		if err != nil {
			zap.L().Error("failed to find normal role", zap.Error(err))
			return err
		}
		// 为普通用户分配工作台和分析页菜单
		var consoleMenu, analysisMenu, dashboardMenu SystemMenu
		err = db.First(&dashboardMenu, 1).Error
		if err != nil {
			zap.L().Error("failed to find dashboard menu", zap.Error(err))
			return err
		}
		err = db.First(&consoleMenu, 7).Error
		if err != nil {
			zap.L().Error("failed to find console menu", zap.Error(err))
			return err
		}
		err = db.First(&analysisMenu, 8).Error
		if err != nil {
			zap.L().Error("failed to find analysis menu", zap.Error(err))
			return err
		}
		err = db.Model(&normalRole).Association("SystemMenus").Append([]SystemMenu{dashboardMenu, consoleMenu, analysisMenu})
		if err != nil {
			zap.L().Error("failed to associate console and analysis menus with normal role", zap.Error(err))
			return err
		}

		// 检查是否已有部门数据
		tx.Model(&SystemDepartment{}).Count(&count)
		if count > 0 {
			zap.L().Info("department data already exists, skipping department creation")
			return nil
		}

		// 创建部门
		departments := []SystemDepartment{
			{Model: gorm.Model{ID: 1}, Name: "管理中心", Sort: 1, Status: 1},
		}
		err = db.Create(&departments).Error
		if err != nil {
			zap.L().Error("failed to create department", zap.Error(err))
			return err
		}

		// 检查是否已有用户数据
		tx.Model(&SystemUser{}).Count(&count)
		if count > 0 {
			zap.L().Info("user data already exists, skipping user creation")
			return nil
		}

		// 创建用户
		pwd := encryptionPWD(config.AdminPassword)
		users := []SystemUser{
			{Model: gorm.Model{ID: 1}, DepartmentID: 1, RoleID: 1, Name: "超级管理员", Username: "admin", Password: pwd, Status: 1, Gender: 1},
		}
		err = db.Create(&users).Error
		if err != nil {
			zap.L().Error("failed to create user", zap.Error(err))
			return err
		}
		return nil
	})
	return err
}

func resetSequences(db *gorm.DB) error {
	tables := []string{
		"system_menus", "system_roles", "system_departments", "system_users",
		"system_menu_auths", // 添加这个表以确保菜单权限序列也被重置
	}

	for _, table := range tables {
		seqName := table + "_id_seq"
		query := fmt.Sprintf("SELECT setval('%s', (SELECT COALESCE(MAX(id), 1) FROM %s));", seqName, table)
		if err := db.Exec(query).Error; err != nil {
			zap.L().Error("failed to reset sequence", zap.String("sequence", seqName), zap.Error(err))
			return err
		}
		zap.L().Info("sequence reset successfully", zap.String("sequence", seqName))
	}
	return nil
}

func Migrate(db *gorm.DB) error {
	err := migrateTable(db)
	if err != nil {
		return err
	}
	err = migrateData(db)
	if err != nil {
		return err
	}
	// 添加序列重置操作
	err = resetSequences(db)
	if err != nil {
		return err
	}
	return nil
}
````

## File: art-design-pro-edge-go-server-main/db/pgdb/system/model.go
````go
package system

import (
	"gorm.io/gorm"
)

// Department 部门表
type SystemDepartment struct {
	gorm.Model
	Name        string       `json:"name,omitempty"`
	Sort        uint         `json:"sort,omitempty"`
	Status      uint         `json:"status,omitempty"`                               // 状态(1:启用 2:禁用)
	SystemUsers []SystemUser `json:"users,omitempty" gorm:"foreignKey:DepartmentID"` // 一对多关联用户表
}

// Role 角色表
type SystemRole struct {
	gorm.Model
	Name            string           `json:"name,omitempty"`
	Desc            string           `json:"desc,omitempty"`
	Status          uint             `json:"status,omitempty"`                                                  // 状态(1:启用 2:禁用)
	SystemMenus     []SystemMenu     `json:"menus,omitempty" gorm:"many2many:system_roles__system_menus;"`      // 多对多关联菜单表
	SystemUsers     []SystemUser     `json:"users,omitempty" gorm:"foreignKey:RoleID"`                          // 一对多关联用户表
	SystemMenuAuths []SystemMenuAuth `json:"menu_auths,omitempty" gorm:"many2many:system_roles__system_auths;"` // 多对多关联菜单按钮权限表
}

// Menu 菜单表
type SystemMenu struct {
	gorm.Model
	Path            string           `json:"path,omitempty"`
	Name            string           `json:"name,omitempty"`
	Component       string           `json:"component,omitempty"`            // vue组件
	Title           string           `json:"title,omitempty"`                // 菜单标题
	Icon            string           `json:"icon,omitempty"`                 // 菜单图标
	ShowBadge       uint             `json:"show_badge,omitempty"`           // 是否显示角标(1:显示 2:隐藏)
	ShowTextBadge   string           `json:"show_text_badge,omitempty"`      // 是否显示文本角标(1:显示 2:隐藏)
	IsHide          uint             `json:"is_hide,omitempty"`              // 是否隐藏(1:隐藏 2:显示)
	IsHideTab       uint             `json:"is_hide_tab,omitempty"`          // 是否隐藏标签(1:隐藏 2:显示)
	Link            string           `json:"link,omitempty"`                 // 链接(外链)
	IsIframe        uint             `json:"is_iframe,omitempty"`            // 是否内嵌(1:内嵌 2:不内嵌)
	KeepAlive       uint             `json:"keep_alive,omitempty"`           // 是否缓存(1:缓存 2:不缓存)
	IsFirstLevel    uint             `json:"is_in_main_container,omitempty"` // 是否在主容器内(一级菜单使用)(1:是 2:否)
	Status          uint             `json:"status,omitempty"`               // 状态(1:启用 2:禁用)
	Level           uint             `json:"level,omitempty"`                // 层级(从1开始)
	ParentID        uint             `json:"parent_id,omitempty"`            // 父级ID
	Sort            uint             `json:"sort,omitempty"`                 // 排序(从大到小)
	SystemRoles     []SystemRole     `json:"roles,omitempty" gorm:"many2many:system_roles__system_menus;"`
	SystemMenuAuths []SystemMenuAuth `json:"menu_auths,omitempty" gorm:"foreignKey:MenuID"` // 一对多关联菜单按钮权限表
}

// MenuPermission 菜单按钮权限表
type SystemMenuAuth struct {
	gorm.Model
	MenuID      uint         `json:"menu_id,omitempty"`
	Mark        string       `json:"mark,omitempty"` // 标识
	Title       string       `json:"title,omitempty"`
	SystemRoles []SystemRole `json:"roles,omitempty" gorm:"many2many:system_roles__system_auths;"` // 多对多关联角色表
}

// User 用户表
type SystemUser struct {
	gorm.Model
	DepartmentID uint   `json:"department_id,omitempty"`
	RoleID       uint   `json:"role_id,omitempty"`
	Name         string `json:"name,omitempty"`     // 昵称
	Username     string `json:"username,omitempty"` // 姓名, 不可修改
	Password     string `json:"password,omitempty"`
	Phone        string `json:"phone,omitempty"`
	Gender       uint   `json:"gender,omitempty"` // 性别(1:男 2:女)
	Status       uint   `json:"status,omitempty"` // 状态(1:启用 2:禁用)
}

type SystemUserLoginLog struct {
	gorm.Model
	UserName string `json:"user_name,omitempty"`
	Password string `json:"password,omitempty"`
	IP       string `json:"ip,omitempty"`
}
````

## File: art-design-pro-edge-go-server-main/db/pgdb/system/role.go
````go
package system

import (
	"go.uber.org/zap"
	"gorm.io/gorm"

	"api-server/config"
	"api-server/db/pgdb"
)

func UpdateRole(role *SystemRole) error {
	if err := pgdb.GetClient().Updates(&role).Error; err != nil {
		zap.L().Error("failed to update role", zap.Error(err))
		return err
	}
	return nil
}

func AddRole(role *SystemRole) error {
	if err := pgdb.GetClient().Create(&role).Error; err != nil {
		zap.L().Error("failed to create role", zap.Error(err))
		return err
	}
	return nil
}

func DeleteRole(role *SystemRole) error {
	if err := pgdb.GetClient().Delete(&role).Error; err != nil {
		zap.L().Error("failed to delete role", zap.Error(err))
		return err
	}
	return nil
}

// GetRole 获取单个角色信息
func GetRole(role *SystemRole) error {
	if err := pgdb.GetClient().Where(role).First(role).Error; err != nil {
		zap.L().Error("failed to get role", zap.Error(err))
		return err
	}
	return nil
}

// FindAllRoles 查询所有角色
func FindAllRoles(roles *[]SystemRole) error {
	if err := pgdb.GetClient().Find(roles).Error; err != nil {
		zap.L().Error("failed to find all roles", zap.Error(err))
		return err
	}
	return nil
}

// FindRoleList 查询角色列表(带分页)
func FindRoleList(role *SystemRole, page, pageSize int) ([]SystemRole, int64, error) {
	var roles []SystemRole
	var total int64
	db := pgdb.GetClient()

	// 构建基础查询
	query := db.Model(&SystemRole{})

	// 应用过滤条件
	if role.Name != "" {
		query = query.Where("name LIKE ?", "%"+role.Name+"%")
	}
	if role.Status != 0 {
		query = query.Where("status = ?", role.Status)
	}

	// 获取符合条件的总记录数
	if err := query.Count(&total).Error; err != nil {
		zap.L().Error("failed to count role list", zap.Error(err))
		return nil, 0, err
	}

	// 构建排序和预加载
	queryWithPreload := query.Preload("SystemUsers", func(db *gorm.DB) *gorm.DB {
		return db.Select("id", "name", "role_id", "created_at", "updated_at") // 只选择需要的字段
	}).Order("id DESC")

	// 判断是否需要分页
	if page == config.CancelPage && pageSize == config.CancelPageSize {
		// 不分页，获取所有数据
		if err := queryWithPreload.Find(&roles).Error; err != nil {
			zap.L().Error("failed to find all role list", zap.Error(err))
			return nil, 0, err
		}
	} else {
		// 应用分页并获取数据
		if err := queryWithPreload.Offset((page - 1) * pageSize).
			Limit(pageSize).
			Find(&roles).Error; err != nil {
			zap.L().Error("failed to find role list with pagination", zap.Error(err))
			return nil, 0, err
		}
	}

	return roles, total, nil
}
````

## File: art-design-pro-edge-go-server-main/db/pgdb/system/user.go
````go
package system

import (
	"errors"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"api-server/config"
	"api-server/db/pgdb"
	"api-server/util/encryption"
)

func encryptionPWD(password string) string {
	return encryption.MD5WithSalt(config.PWDSalt + password)
}

// 查询用户
func VerifyUser(userName, password string) (SystemUser, error) {
	user := SystemUser{}
	err := pgdb.GetClient().Where(&SystemUser{Username: userName, Password: encryptionPWD(password)}).First(&user).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return user, nil
		}
		zap.L().Error("failed to get user", zap.Error(err))
		return user, err
	}
	return user, nil
}

// 记录用户登录日志
func CreateLoginLog(log *SystemUserLoginLog) error {
	if err := pgdb.GetClient().Create(log).Error; err != nil {
		zap.L().Error("failed to record login log", zap.Error(err))
		return err
	}
	return nil
}

// FindLoginLogList 查询登录日志列表，支持分页和按用户名、IP查询
func FindLoginLogList(loginLog *SystemUserLoginLog, page, pageSize int) ([]SystemUserLoginLog, int64, error) {
	var loginLogs []SystemUserLoginLog
	var total int64
	db := pgdb.GetClient()

	// 构建基础查询
	baseQuery := db.Model(&SystemUserLoginLog{}).Where("deleted_at IS NULL")

	// 使用模糊查询
	if loginLog.UserName != "" {
		baseQuery = baseQuery.Where("user_name LIKE ?", "%"+loginLog.UserName+"%")
	}
	if loginLog.IP != "" {
		baseQuery = baseQuery.Where("ip LIKE ?", "%"+loginLog.IP+"%")
	}

	// 获取符合条件的总记录数
	baseQuery.Count(&total)

	// 判断是否需要分页
	if page == config.CancelPage && pageSize == config.CancelPageSize {
		// 不分页，获取所有数据
		if err := baseQuery.Order("created_at DESC").Find(&loginLogs).Error; err != nil {
			zap.L().Error("failed to find all login logs", zap.Error(err))
			return nil, 0, err
		}
	} else {
		// 应用分页并获取数据
		if err := baseQuery.Order("created_at DESC").Offset((page - 1) * pageSize).Limit(pageSize).Find(&loginLogs).Error; err != nil {
			zap.L().Error("failed to find login logs", zap.Error(err))
			return nil, 0, err
		}
	}

	return loginLogs, total, nil
}

// UserWithRelations 包含用户及其关联的角色和部门信息
type UserWithRelations struct {
	SystemUser     `json:"User"`
	RoleName       string `json:"role_name"`
	RoleDesc       string `json:"role_desc"`
	DepartmentName string `json:"department_name"`
}

func FindUserList(user *SystemUser, page, pageSize int) ([]UserWithRelations, int64, error) {
	var usersWithRelations []UserWithRelations
	var total int64
	db := pgdb.GetClient()
	// 构建基础查询
	baseQuery := db.Table("system_users").
		Joins("left join system_roles on system_users.role_id = system_roles.id").
		Joins("left join system_departments on system_users.department_id = system_departments.id").
		Where("system_users.deleted_at IS NULL")
	// 使用模糊查询
	if user.Username != "" {
		baseQuery = baseQuery.Where("system_users.username LIKE ?", "%"+user.Username+"%")
	}
	if user.Name != "" {
		baseQuery = baseQuery.Where("system_users.name LIKE ?", "%"+user.Name+"%")
	}
	if user.Phone != "" {
		baseQuery = baseQuery.Where("system_users.phone LIKE ?", "%"+user.Phone+"%")
	}
	if user.RoleID != 0 {
		baseQuery = baseQuery.Where("system_users.role_id = ?", user.RoleID)
	}
	if user.DepartmentID != 0 {
		baseQuery = baseQuery.Where("system_users.department_id = ?", user.DepartmentID)
	}
	// 获取符合条件的总记录数
	baseQuery.Count(&total)

	// 准备查询对象，添加选择字段和排序
	query := baseQuery.Select("system_users.*, system_roles.name as role_name, system_roles.desc as role_desc, system_departments.name as department_name")

	// 判断是否需要分页
	if page == config.CancelPage && pageSize == config.CancelPageSize {
		// 不分页，获取所有数据
		if err := query.Find(&usersWithRelations).Error; err != nil {
			zap.L().Error("failed to find all user list", zap.Error(err))
			return nil, 0, err
		}
	} else {
		// 应用分页并获取数据
		if err := query.Offset((page - 1) * pageSize).Limit(pageSize).Find(&usersWithRelations).Error; err != nil {
			zap.L().Error("failed to find user list", zap.Error(err))
			return nil, 0, err
		}
	}

	return usersWithRelations, total, nil
}

func AddUser(user *SystemUser) error {
	user.Password = encryptionPWD(user.Password)
	if err := pgdb.GetClient().Create(user).Error; err != nil {
		zap.L().Error("failed to add user", zap.Error(err))
		return err
	}
	return nil
}

func GetUser(user *SystemUser) error {
	if err := pgdb.GetClient().Where(user).First(user).Error; err != nil {
		zap.L().Error("failed to get user", zap.Error(err))
		return err
	}
	return nil
}

func UpdateUser(user *SystemUser) error {
	if user.Password != "" {
		user.Password = encryptionPWD(user.Password)
	}
	if err := pgdb.GetClient().Updates(user).Error; err != nil {
		zap.L().Error("failed to update user", zap.Error(err))
		return err
	}
	return nil
}

func DeleteUser(user *SystemUser) error {
	if err := pgdb.GetClient().Delete(user).Error; err != nil {
		zap.L().Error("failed to delete user", zap.Error(err))
		return err
	}
	return nil
}

// FindAllUsers 查询所有用户
func FindAllUsers(users *[]SystemUser) error {
	if err := pgdb.GetClient().Find(users).Error; err != nil {
		zap.L().Error("failed to find all users", zap.Error(err))
		return err
	}
	return nil
}
````

## File: art-design-pro-edge-go-server-main/db/pgdb/client.go
````go
package pgdb

import (
	"time"

	"api-server/config"

	"go.uber.org/zap"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

var client *gorm.DB

func GetClient() *gorm.DB {
	if client == nil {
		Init()
	}
	return client
}

// Connect to the database
func Init() error {
	db, err := gorm.Open(postgres.Open(config.PgsqlDSN), &gorm.Config{})
	if err != nil {
		zap.L().Error("connect to mysql failed", zap.Error(err))
		return err
	}
	pgDB, err := db.DB()
	if err != nil {
		zap.L().Error("get db failed", zap.Error(err))
		return err
	}
	// SetMaxIdleConns sets the maximum number of connections in the idle connection pool.
	pgDB.SetMaxIdleConns(10)
	// SetMaxOpenConns sets the maximum number of open connections to the database.
	pgDB.SetMaxOpenConns(100)
	// SetConnMaxLifetime sets the maximum amount of time a connection may be reused.
	pgDB.SetConnMaxLifetime(time.Hour)
	client = db
	return nil
}
````

## File: art-design-pro-edge-go-server-main/db/rdb/captcha/store.go
````go
package captcha

import (
	"fmt"
	"time"

	"api-server/db/rdb"

	"github.com/mojocn/base64Captcha"
	"go.uber.org/zap"
)

const (
	// 每个验证码存活5分钟
	DefaultRedisExpiration = 5 * time.Minute
	// 存在 redis 中的 key 前缀
	DefaultRedisPrefixKey = "captcha"
)

type redisStore struct {
	expiration time.Duration
	prefixKey  string
}

var store base64Captcha.Store

func GetRedisStore() base64Captcha.Store {
	if store == nil {
		store = newRedisStore(DefaultRedisExpiration, DefaultRedisPrefixKey)
	}
	return store
}

// redis store
func newRedisStore(expiration time.Duration, prefixKey string) base64Captcha.Store {
	s := new(redisStore)
	s.expiration = expiration
	s.prefixKey = prefixKey
	if s.prefixKey == "" {
		s.prefixKey = DefaultRedisPrefixKey
	}
	if s.expiration == 0 {
		s.expiration = DefaultRedisExpiration
	}
	return s
}

// set
func (s *redisStore) Set(id string, value string) error {
	c := rdb.GetClient()
	k := fmt.Sprintf("%s-%s", s.prefixKey, id)
	_, err := c.SetNX(k, value, s.expiration).Result()
	if err != nil {
		zap.L().Error("redis set key", zap.Error(err))
	}
	return err
}

// get
func (s *redisStore) Get(id string, clear bool) string {
	c := rdb.GetClient()
	k := fmt.Sprintf("%s-%s", s.prefixKey, id)
	v, err := c.Get(k).Result()
	if err != nil {
		zap.L().Error("redis get key", zap.Error(err))
		return ""
	}
	return v
}

// verify
func (s *redisStore) Verify(id, answer string, clear bool) bool {
	c := rdb.GetClient()
	k := fmt.Sprintf("%s-%s", s.prefixKey, id)
	v, err := c.Get(k).Result()
	if err != nil {
		zap.L().Error("redis verify key", zap.Error(err))
		return false
	}
	if v == answer {
		if clear {
			_, err = c.Del(k).Result()
			if err != nil {
				zap.L().Error("redis verify del key", zap.Error(err))
				return false
			}
		}
		return true
	}
	return false
}
````

## File: art-design-pro-edge-go-server-main/db/rdb/systemUser/user.go
````go
package systemuser

import (
	"encoding/json"
	"strconv"
	"time"

	"github.com/go-redis/redis"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"api-server/db/pgdb/system"
	"api-server/db/rdb"
)

const (
	// UserInfoKey 用户信息缓存键前缀
	UserInfoKey = "system:user:info:"
	// UserListKey 用户列表缓存键
	UserListKey = "system:user:list"
	// UserCacheExpiration 用户缓存过期时间（12小时）
	UserCacheExpiration = 12 * time.Hour
)

// UserCacheInfo 用户缓存信息
type UserCacheInfo struct {
	ID       uint   `json:"id"`
	Username string `json:"username"`
	Name     string `json:"name"`
	RoleID   uint   `json:"role_id"`
	RoleName string `json:"role_name"`
}

// CacheAllUsers 缓存所有用户信息到Redis
func CacheAllUsers() error {
	client := rdb.GetClient()

	// 获取所有用户信息
	var users []system.SystemUser
	if err := system.FindAllUsers(&users); err != nil {
		zap.L().Error("获取所有用户信息失败", zap.Error(err))
		return err
	}

	// 获取所有角色信息，用于映射角色名称
	var roles []system.SystemRole
	if err := system.FindAllRoles(&roles); err != nil {
		zap.L().Error("获取所有角色信息失败", zap.Error(err))
		return err
	}

	// 创建角色映射表
	roleMap := make(map[uint]string)
	for _, role := range roles {
		roleMap[role.ID] = role.Name
	}

	// 使用管道批量操作，提高效率
	pipe := client.Pipeline()

	// 缓存用户列表
	var userList []UserCacheInfo
	for _, user := range users {
		// 创建用户缓存对象
		userCache := UserCacheInfo{
			ID:       user.ID,
			Username: user.Username,
			Name:     user.Name,
			RoleID:   user.RoleID,
			RoleName: roleMap[user.RoleID],
		}

		// 将用户信息添加到列表
		userList = append(userList, userCache)

		// 单独缓存每个用户信息，方便通过ID快速获取
		userJSON, err := json.Marshal(userCache)
		if err != nil {
			zap.L().Error("序列化用户信息失败", zap.Error(err))
			continue
		}
		pipe.Set(UserInfoKey+strconv.FormatUint(uint64(user.ID), 10), userJSON, UserCacheExpiration)
	}

	// 缓存完整用户列表
	listJSON, err := json.Marshal(userList)
	if err != nil {
		zap.L().Error("序列化用户列表失败", zap.Error(err))
		return err
	}
	pipe.Set(UserListKey, listJSON, UserCacheExpiration)

	// 执行管道操作
	_, err = pipe.Exec()
	if err != nil {
		zap.L().Error("缓存用户信息到Redis失败", zap.Error(err))
		return err
	}

	zap.L().Info("已成功缓存所有用户信息到Redis", zap.Int("用户数量", len(users)))
	return nil
}

// GetUserFromCache 从缓存中获取用户信息
func GetUserFromCache(userID uint) (*UserCacheInfo, error) {
	client := rdb.GetClient()

	// 从Redis获取用户信息
	val, err := client.Get(UserInfoKey + strconv.FormatUint(uint64(userID), 10)).Result()
	if err != nil {
		if err == redis.Nil {
			// 缓存未命中，尝试单独获取并缓存该用户
			return cacheUserByID(userID)
		}
		zap.L().Error("从Redis获取用户信息失败", zap.Error(err))
		return nil, err
	}

	// 反序列化用户信息
	var userCache UserCacheInfo
	if err = json.Unmarshal([]byte(val), &userCache); err != nil {
		zap.L().Error("反序列化用户信息失败", zap.Error(err))
		return nil, err
	}

	return &userCache, nil
}

// GetAllUsersFromCache 从缓存中获取所有用户列表
func GetAllUsersFromCache() ([]UserCacheInfo, error) {
	client := rdb.GetClient()

	// 从Redis获取用户列表
	val, err := client.Get(UserListKey).Result()
	if err != nil {
		if err == redis.Nil {
			// 缓存未命中，重新缓存所有用户
			if err = CacheAllUsers(); err != nil {
				return nil, err
			}
			// 再次尝试获取
			val, err = client.Get(UserListKey).Result()
			if err != nil {
				zap.L().Error("从Redis获取用户列表失败", zap.Error(err))
				return nil, err
			}
		} else {
			zap.L().Error("从Redis获取用户列表失败", zap.Error(err))
			return nil, err
		}
	}

	// 反序列化用户列表
	var userList []UserCacheInfo
	if err = json.Unmarshal([]byte(val), &userList); err != nil {
		zap.L().Error("反序列化用户列表失败", zap.Error(err))
		return nil, err
	}

	return userList, nil
}

// cacheUserByID 单独获取并缓存指定ID的用户
func cacheUserByID(userID uint) (*UserCacheInfo, error) {
	client := rdb.GetClient()

	// 获取用户信息
	user := system.SystemUser{Model: gorm.Model{ID: userID}}
	if err := system.GetUser(&user); err != nil {
		zap.L().Error("获取用户信息失败", zap.Error(err))
		return nil, err
	}

	// 获取角色信息
	role := system.SystemRole{Model: gorm.Model{ID: user.RoleID}}
	if err := system.GetRole(&role); err != nil {
		zap.L().Error("获取角色信息失败", zap.Error(err))
		// 继续执行，只是角色名称可能为空
	}

	// 创建用户缓存对象
	userCache := UserCacheInfo{
		ID:       user.ID,
		Username: user.Username,
		Name:     user.Name,
		RoleID:   user.RoleID,
		RoleName: role.Name,
	}

	// 序列化用户信息
	userJSON, err := json.Marshal(userCache)
	if err != nil {
		zap.L().Error("序列化用户信息失败", zap.Error(err))
		return nil, err
	}

	// 缓存用户信息
	if err = client.Set(UserInfoKey+strconv.FormatUint(uint64(user.ID), 10), userJSON, UserCacheExpiration).Err(); err != nil {
		zap.L().Error("缓存用户信息到Redis失败", zap.Error(err))
		return nil, err
	}

	return &userCache, nil
}
````

## File: art-design-pro-edge-go-server-main/db/rdb/client.go
````go
package rdb

import (
	"github.com/go-redis/redis"
	"go.uber.org/zap"

	"api-server/config"
)

// redisDB redis连接池
var client *redis.Client

// InitRedisClient 初始化客户端连接池
func Init() error {
	client = redis.NewClient(&redis.Options{
		Addr:         config.RedisHost,
		Password:     config.RedisPassword,
		PoolSize:     100,
		MinIdleConns: 50,
	})
	if err := client.Ping().Err(); err != nil {
		zap.L().Error("redis连接失败", zap.Error(err))
		return err
	}
	return nil
}

func GetClient() *redis.Client {
	if client == nil {
		Init()
	}
	return client
}

func CloseClient() {
	client.Close()
	client = nil
}
````

## File: art-design-pro-edge-go-server-main/docker/docker-compose.yml
````yaml
services:
  redis:
    image: redis:7.4
    container_name: server-redis # 注意：redis服务的container_name也应该修改
    command: ["redis-server", "--requirepass", "izpXvn894uW2HFbyP5OGr"] # TODO Redis 密码, 务必修改为自己的密码, 防止泄露
    ports:
      - "6379:6379"
    restart: always
  postgres:
    image: postgres:17.4
    container_name: server-postgres
    environment:
      POSTGRES_PASSWORD: kL81xnDWo221FHFRX8GnP # TODO 设置超级用户密码, 务必修改为自己的密码, 防止泄露
      POSTGRES_USER: postgres # 默认用户（可选，默认值也是 postgres）
      POSTGRES_DB: server # 初始数据库（可选）
      TZ: Asia/Shanghai # 设置时区为上海
    ports:
      - "5432:5432" # 映射宿主机端口:容器端口（允许外网访问）
    volumes:
      - ./postgres_data:/var/lib/postgresql/data # 数据持久化路径,根据自己需要进行修改
    restart: always # 容器自动重启
````

## File: art-design-pro-edge-go-server-main/util/authentication/jwt.go
````go
package authentication

import (
	"fmt"
	"time"

	"api-server/config"
	"api-server/util/id"

	"github.com/golang-jwt/jwt/v5"
)

var issue string = "server"
var subject string = "token"
var audienc string = "client"

type MyCustomClaims struct {
	Data string `json:"data"`
	jwt.RegisteredClaims
}

// JWTIssue issue jwt
func JWTIssue(data string) (string, error) {
	// set key
	mySigningKey := []byte(config.JWTKey)
	// Calculate expiration time
	nt := time.Now()
	exp := nt.Add(config.JWTExpiration)
	// Create the Claims
	claims := MyCustomClaims{
		data,
		jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(exp),
			Issuer:    issue,
			IssuedAt:  jwt.NewNumericDate(nt),
			Subject:   subject,
			Audience:  jwt.ClaimStrings{audienc},
			NotBefore: jwt.NewNumericDate(nt),
			ID:        id.IssueMd5ID(),
		},
	}
	// https://en.wikipedia.org/wiki/JSON_Web_Token
	// issue
	t := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	st, err := t.SignedString(mySigningKey)
	if err != nil {
		return "", err
	}
	return st, nil
}

// JWTDecrypt string token to data
func JWTDecrypt(tokenString string) (string, error) {
	t, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		// HMAC Check
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(config.JWTKey), nil
	})
	if err != nil {
		return "", err
	}
	if !t.Valid {
		return "", fmt.Errorf("invalid token")
	}
	if claims, ok := t.Claims.(jwt.MapClaims); ok && t.Valid {
		return claims["data"].(string), nil
	} else {
		return "", err
	}
}
````

## File: art-design-pro-edge-go-server-main/util/encryption/md5.go
````go
package encryption

import (
	"crypto/md5"
	"encoding/hex"
)

// MD5WithSalt encrypts a string with a salt using MD5 algorithm
func MD5WithSalt(input string) string {
	// Concatenate the original string with the salt
	salted := input

	// Create a new MD5 hash
	hasher := md5.New()

	// Write the salted string as bytes to the hasher
	hasher.Write([]byte(salted))

	// Get the resulting hash as bytes
	hashBytes := hasher.Sum(nil)

	// Convert hash bytes to hexadecimal string
	hashString := hex.EncodeToString(hashBytes)

	return hashString
}
````

## File: art-design-pro-edge-go-server-main/util/id/id.go
````go
package id

import (
	"crypto/md5"
	"fmt"

	"github.com/sony/sonyflake"
)

var flake *sonyflake.Sonyflake

func initSonyFlake() {
	flake = sonyflake.NewSonyflake(sonyflake.Settings{})
}

// IssueID Unique ID generated using Sony's improved twite snowflake algorithm
// https://github.com/sony/sonyflake
func IssueID() string {
	if flake == nil {
		initSonyFlake()
	}
	id, _ := flake.NextID()
	return fmt.Sprintf("%v", id)
}

func IssueMd5ID() string {
	keyID := IssueID()
	id := fmt.Sprintf("%x", md5.Sum([]byte(keyID)))
	return id
}

func init() {
	flake = sonyflake.NewSonyflake(sonyflake.Settings{})
}
````

## File: art-design-pro-edge-go-server-main/util/log/log.go
````go
package log

import (
	"os"
	"time"

	"api-server/config"
	runmodel "api-server/util/run-model"

	"github.com/fsnotify/fsnotify"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
)

var logger *zap.Logger

// Creating Dev logger
// DEV mode outputs logs to the terminal and is more readable
func createDevLogger() *zap.Logger {
	encoder := zap.NewDevelopmentEncoderConfig()
	core := zapcore.NewTee(
		zapcore.NewSamplerWithOptions(
			zapcore.NewCore(zapcore.NewConsoleEncoder(encoder), os.Stdout, zap.DebugLevel), time.Second, 4, 1),
	)
	return zap.New(core, zap.AddCaller())
}

// Creating product logger
// The product pattern outputs logs to a file and is architecturally structured, in json format.
func createProductLogger(fileName string) *zap.Logger {
	fileEncoder := zap.NewProductionEncoderConfig()
	fileEncoder.EncodeTime = zapcore.ISO8601TimeEncoder
	fileWriter := zapcore.AddSync(&lumberjack.Logger{
		Filename:   fileName,
		MaxSize:    config.LogMaxSize,
		MaxBackups: config.LogMaxBackups,
		MaxAge:     config.LogMaxAge,
	})
	core := zapcore.NewTee(
		zapcore.NewSamplerWithOptions(
			zapcore.NewCore(zapcore.NewJSONEncoder(fileEncoder), fileWriter, zap.InfoLevel), time.Second, 4, 1),
	)
	return zap.New(core, zap.AddCaller())
}

// SetLogger to prevent zap persistence problems after files are deleted
func SetLogger() {
	// Get log mode
	switch {
	case runmodel.IsDev():
		logger = createDevLogger()
	case runmodel.IsRelease():
		logger = createProductLogger(config.LogPath)
	default:
		logger = createProductLogger(config.LogPath)
	}
	zap.ReplaceGlobals(logger)
}

// Listen to log files
// When the log file is deleted manually, we will automatically create a new one.
func monitorFile() {
	watcher, err := fsnotify.NewWatcher()
	if err != nil {
		zap.L().Error("File listening error", zap.Error(err))
		return
	}
	defer watcher.Close()
	err = watcher.Add(config.LogPath)
	if err != nil {
		zap.L().Error("File listening error", zap.Error(err))
	}
	for {
		select {
		case event := <-watcher.Events:
			if event.Has(fsnotify.Remove) {
				zap.L().Warn("the log file was deleted")
				SetLogger()
			}
			if event.Has(fsnotify.Rename) {
				zap.L().Warn("log files are renamed and new files are monitored")
				SetLogger()
			}
		case err := <-watcher.Errors:
			zap.L().Error("file listening error", zap.Error(err))
		}
	}
}

func GetLogger() *zap.Logger {
	return logger
}

func init() {
	SetLogger()
	go monitorFile()
}
````

## File: art-design-pro-edge-go-server-main/util/path-tool/path.go
````go
package pathtool

import (
	"os"
	"path/filepath"
	"strings"
)

// Get the directory where the current program is located
func GetCurrentDirectory() string {
	dir, _ := filepath.Abs(filepath.Dir(os.Args[0]))
	return strings.Replace(dir, "\\", "/", -1)
}

// Determine if the path exists
func PathExists(path string) (bool, error) {
	_, err := os.Stat(path)
	if err == nil {
		return true, nil
	}
	if os.IsNotExist(err) {
		return false, nil
	}
	return false, err
}

// create folders
func CreateDir(path string) error {
	exist, err := PathExists(path)
	if err != nil {
		return err
	}
	if exist {
		return nil
	} else {
		err := os.MkdirAll(path, os.ModePerm)
		if err != nil {
			return err
		}
	}
	return nil
}

// create a file
func CreateFile(path string) error {
	dir := filepath.Dir(path)
	CreateDir(dir)
	exist, err := PathExists(path)
	if err != nil {
		return err
	}
	if exist {
		return nil
	} else {
		err := os.WriteFile(path, []byte(""), 0644)
		if err != nil {
			return err
		}
	}
	return nil
}
````

## File: art-design-pro-edge-go-server-main/util/run-model/model.go
````go
package runmodel

import (
	"os"

	"api-server/config"
)

// Detect the running mode of the program
// default is dev
func Detection() {
	model := os.Getenv(config.RunModelKey)
	switch model {
	case config.RunModelDevValue:
		config.RunModel = config.RunModelDevValue
	case config.RunModelRelease:
		config.RunModel = config.RunModelRelease
	default:
		config.RunModel = config.RunModelRelease
	}
}

func IsDev() bool {
	return config.RunModel == config.RunModelDevValue
}

func IsRelease() bool {
	return config.RunModel == config.RunModelRelease
}
````

## File: art-design-pro-edge-go-server-main/.gitignore
````
# Build
bulid/
*.exe

# dotenv
.env

# venv
/vendor/

# Log
*.log

# OSX dir files
.DS_Store

# Sublime Text settings
*.sublime-workspace
*.sublime-project

server
/docker/postgres_data/
````

## File: art-design-pro-edge-go-server-main/go.mod
````
module api-server

go 1.24.1

require (
	github.com/fsnotify/fsnotify v1.9.0
	github.com/gin-gonic/gin v1.10.0
	github.com/go-co-op/gocron/v2 v2.16.1
	github.com/go-redis/redis v6.15.9+incompatible
	github.com/golang-jwt/jwt/v5 v5.2.2
	github.com/mojocn/base64Captcha v1.3.8
	github.com/sony/sonyflake v1.2.0
	go.uber.org/zap v1.27.0
	gopkg.in/natefinch/lumberjack.v2 v2.2.1
	gorm.io/driver/postgres v1.5.11
	gorm.io/gorm v1.25.12
)

require (
	github.com/bytedance/sonic v1.13.2 // indirect
	github.com/bytedance/sonic/loader v0.2.4 // indirect
	github.com/cloudwego/base64x v0.1.5 // indirect
	github.com/gabriel-vasile/mimetype v1.4.8 // indirect
	github.com/gin-contrib/sse v1.1.0 // indirect
	github.com/go-playground/locales v0.14.1 // indirect
	github.com/go-playground/universal-translator v0.18.1 // indirect
	github.com/go-playground/validator/v10 v10.26.0 // indirect
	github.com/goccy/go-json v0.10.5 // indirect
	github.com/golang/freetype v0.0.0-20170609003504-e2365dfdc4a0 // indirect
	github.com/google/go-cmp v0.7.0 // indirect
	github.com/google/uuid v1.6.0 // indirect
	github.com/jackc/pgpassfile v1.0.0 // indirect
	github.com/jackc/pgservicefile v0.0.0-20240606120523-5a60cdf6a761 // indirect
	github.com/jackc/pgx/v5 v5.7.4 // indirect
	github.com/jackc/puddle/v2 v2.2.2 // indirect
	github.com/jinzhu/inflection v1.0.0 // indirect
	github.com/jinzhu/now v1.1.5 // indirect
	github.com/jonboulle/clockwork v0.5.0 // indirect
	github.com/json-iterator/go v1.1.12 // indirect
	github.com/klauspost/cpuid/v2 v2.2.10 // indirect
	github.com/leodido/go-urn v1.4.0 // indirect
	github.com/mattn/go-isatty v0.0.20 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/onsi/ginkgo v1.16.5 // indirect
	github.com/onsi/gomega v1.37.0 // indirect
	github.com/pelletier/go-toml/v2 v2.2.4 // indirect
	github.com/robfig/cron/v3 v3.0.1 // indirect
	github.com/rogpeppe/go-internal v1.14.1 // indirect
	github.com/twitchyliquid64/golang-asm v0.15.1 // indirect
	github.com/ugorji/go/codec v1.2.12 // indirect
	go.uber.org/multierr v1.11.0 // indirect
	golang.org/x/arch v0.16.0 // indirect
	golang.org/x/crypto v0.37.0 // indirect
	golang.org/x/image v0.26.0 // indirect
	golang.org/x/net v0.39.0 // indirect
	golang.org/x/sync v0.13.0 // indirect
	golang.org/x/sys v0.32.0 // indirect
	golang.org/x/text v0.24.0 // indirect
	google.golang.org/protobuf v1.36.6 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
)
````

## File: art-design-pro-edge-go-server-main/LICENSE
````
MIT License

Copyright (c) 2025 ChnMig

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
````

## File: art-design-pro-edge-go-server-main/main.go
````go
package main

import (
	"fmt"
	"os"
	"os/signal"
	"syscall"

	"api-server/api"
	"api-server/common/cron"
	"api-server/config"
	"api-server/db/pgdb"
	"api-server/db/pgdb/system"
	"api-server/util/log"

	"go.uber.org/zap"
)

func migrate() error {
	err := system.Migrate(pgdb.GetClient())
	if err != nil {
		zap.L().Error("migrate failed", zap.Error(err))
		return err
	}
	zap.L().Info("migration completed successfully")
	return nil
}

func main() {
	for _, arg := range os.Args[1:] {
		if arg == "--migrate" {
			config.RunModel = config.RunModelDevValue
			log.SetLogger()
			migrate()
			return
		}
		if arg == "--dev" {
			config.RunModel = config.RunModelDevValue
		}
	}
	log.SetLogger()

	// 初始化定时任务
	cron.InitCronJobs()

	r := api.InitApi()
	go r.Run(fmt.Sprintf(":%d", config.ListenPort))

	// 监听停止信号
	sigs := make(chan os.Signal, 1)
	signal.Notify(sigs, syscall.SIGTERM, syscall.SIGINT)
	for {
		sig := <-sigs
		switch sig {
		case syscall.SIGTERM, syscall.SIGINT:
			zap.L().Info("接收到停止信号，程序即将退出", zap.String("signal", sig.String()))
			return
		}
	}
}
````

## File: art-design-pro-edge-go-server-main/README.md
````markdown
# 关于

本项目是 [art-design-pro-edge](https://github.com/ChnMig/art-design-pro-edge) 的后端服务。
配合前端可以做到开箱即用, 但是具体的业务功能需要自己开发.

## 项目特点

- 项目的95%代码由 `github copilot` 辅助编写

## TODO

- API层权限管制
- 接口文档
- 单元测试
- 持续的代码优化

## 部署配套服务

PostgreSQL 和 Redis 的 docker-compose 文件在 `docker` 目录下, 可以直接使用。

> 如果部署在云端, 务必修改有 TODO 标识的配置项, 防止密码泄露!!!

```bash
docker-compose -f docker/docker-compose.yml up -d
```

## 技术栈

`Golang` `Gin` `Gorm` `PostgreSQL` `Redis`

## build

`CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o server`

## dev

`go run main.go --dev`

## 初次启动

### 修改配置文件

> 务必修改配置文件, 其中写 TODO 的务必修改!!!

修改 `config/config.go` 中的配置(尤其是 TODO 标识的配置项)

## 执行数据库初始化

`go run main.go --migrate`

## start

`nohup ./server &`

## QA

### 为什么不做动态配置

动态配置意味着需要将数据库地址存储在配置文件中，如果服务器被入侵, 会导致数据库地址泄露。所以我们不提供数据库地址的动态配置。
而将数据库地址写死在代码中, 可以避免这种情况的发生。
我们考虑过对文件进行加密, 但是这样带来的好处并不是很大, 考虑到用户是有开发经验的, 所以我们认为用户可以查看文档通过修改代码的方式进行配置
````
